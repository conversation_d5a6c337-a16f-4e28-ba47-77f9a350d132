"""
EEG Motor Movement/Imagery Dataset 数据加载器

用于加载和预处理PhysioNet EEG Motor Movement/Imagery Dataset数据集
专门用于运动想象BCI算法验证
集成实际使用的预处理管道，确保验证结果与实际性能一致
"""

import numpy as np
import os
import logging
from typing import List, Tuple, Dict, Optional
import mne
from mne.io import read_raw_edf
import pandas as pd

# 导入预处理管道
from services.eeg_preprocessing import EEGPreprocessingPipeline, PreprocessingConfig, QuickConfigs

logger = logging.getLogger(__name__)

class EEGDatasetLoader:
    """EEG Motor Movement/Imagery Dataset数据加载器"""

    def __init__(self, dataset_path: str, enable_preprocessing: bool = True):
        """
        初始化数据加载器

        Args:
            dataset_path: 数据集根目录路径
            enable_preprocessing: 是否启用预处理管道
        """
        self.dataset_path = dataset_path
        self.enable_preprocessing = enable_preprocessing
        
        # 系统指定的8个电极（严格按照系统设计文档）
        # 这些电极与系统配置完全一致，确保验证结果的有效性
        self.motor_channels = [
            'PZ', 'P3', 'P4',     # 顶叶区
            'C3', 'CZ', 'C4',     # 中央运动区
            'F3', 'F4'            # 前额区
        ]
        
        # 运动想象任务文件（包含平静和运动想象状态）
        self.motor_imagery_runs = ['R06', 'R10']  # R14也可以，但先用这两个
        
        # 事件代码映射 - 二分类：平静 vs 运动想象
        self.event_mapping = {
            'T0': 0,  # 平静/休息状态
            'T1': 1,  # 运动想象 (双手)
            'T2': 1   # 运动想象 (双脚) - 与T1合并为同一类
        }
        
        # 初始化预处理管道（如果启用）
        if self.enable_preprocessing:
            # 使用与实际系统一致的预处理配置
            self.preprocessing_config = QuickConfigs.traditional_and_rls()
            self.preprocessing_pipeline = EEGPreprocessingPipeline(self.preprocessing_config)
            logger.info("预处理管道已启用：传统滤波 + RLS自适应滤波")
        else:
            self.preprocessing_pipeline = None
            logger.info("预处理管道已禁用")

        logger.info(f"EEG数据集加载器初始化完成")
        logger.info(f"数据集路径: {dataset_path}")
        logger.info(f"选择的电极: {self.motor_channels}")
        logger.info(f"运动想象任务: {self.motor_imagery_runs}")
        logger.info(f"预处理状态: {'启用' if self.enable_preprocessing else '禁用'}")
    
    def load_subject_data(self, subject_id: str, 
                         target_fs: int = 125,
                         trial_duration: float = 3.0) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载单个受试者的运动想象数据
        
        Args:
            subject_id: 受试者ID（如'S001'）
            target_fs: 目标采样率
            trial_duration: 试次时长（秒）
            
        Returns:
            tuple: (X, y) 其中X为[n_trials, n_channels, n_samples], y为[n_trials]
        """
        try:
            subject_path = os.path.join(self.dataset_path, subject_id)
            if not os.path.exists(subject_path):
                raise FileNotFoundError(f"受试者目录不存在: {subject_path}")
            
            all_trials = []
            all_labels = []
            
            # 加载每个运动想象任务文件
            for run in self.motor_imagery_runs:
                edf_file = os.path.join(subject_path, f"{subject_id}{run}.edf")
                event_file = os.path.join(subject_path, f"{subject_id}{run}.edf.event")
                
                if not os.path.exists(edf_file) or not os.path.exists(event_file):
                    logger.warning(f"文件不存在: {edf_file} 或 {event_file}")
                    continue
                
                # 加载EEG数据
                raw = read_raw_edf(edf_file, preload=True, verbose=False)

                # 打印可用电极名称（调试用）
                logger.debug(f"可用电极: {raw.ch_names[:10]}...")  # 只打印前10个

                # 严格按照系统指定的8个电极进行映射
                # PhysioNet数据集使用不同的命名格式，需要精确映射
                motor_channel_mapping = {
                    'PZ': ['Pz..', 'Pz', 'EEG Pz'],
                    'P3': ['P3..', 'P3', 'EEG P3'],
                    'P4': ['P4..', 'P4', 'EEG P4'],
                    'C3': ['C3..', 'C3', 'EEG C3'],
                    'CZ': ['Cz..', 'Cz', 'EEG Cz'],
                    'C4': ['C4..', 'C4', 'EEG C4'],
                    'F3': ['F3..', 'F3', 'EEG F3'],
                    'F4': ['F4..', 'F4', 'EEG F4']
                }

                selected_channels = []
                missing_channels = []

                # 严格按照指定顺序查找电极
                for target_ch in self.motor_channels:
                    found = False
                    possible_names = motor_channel_mapping[target_ch]
                    for possible_name in possible_names:
                        if possible_name in raw.ch_names:
                            selected_channels.append(possible_name)
                            found = True
                            break

                    if not found:
                        missing_channels.append(target_ch)

                # 检查是否找到了所有必需的电极
                if len(selected_channels) != 8:
                    logger.error(f"无法找到所有指定电极!")
                    logger.error(f"找到的电极 ({len(selected_channels)}): {selected_channels}")
                    logger.error(f"缺失的电极: {missing_channels}")
                    logger.error(f"可用电极: {raw.ch_names}")
                    raise ValueError(f"无法找到系统指定的8个电极: {missing_channels}")

                logger.info(f"成功映射所有8个指定电极: {selected_channels}")
                raw.pick(selected_channels)
                
                # 降采样到目标采样率
                if raw.info['sfreq'] != target_fs:
                    raw.resample(target_fs)
                
                # 使用MNE读取事件
                try:
                    events, event_id = mne.events_from_annotations(raw)
                    logger.debug(f"MNE事件: {len(events)} 个, 事件ID: {event_id}")

                    # 提取试次数据
                    trials, labels = self._extract_trials_from_mne_events(raw, events, event_id, trial_duration, target_fs)
                except Exception as e:
                    logger.warning(f"MNE事件读取失败: {e}，尝试手动解析")
                    # 备用方案：手动解析事件文件
                    events = self._load_events(event_file)
                    trials, labels = self._extract_trials(raw, events, trial_duration, target_fs)
                
                all_trials.extend(trials)
                all_labels.extend(labels)
                
                logger.info(f"加载 {subject_id}{run}: {len(trials)} 个试次")
            
            if not all_trials:
                raise ValueError(f"未能加载任何试次数据: {subject_id}")
            
            # 转换为numpy数组
            X = np.array(all_trials)  # [n_trials, n_channels, n_samples]
            y = np.array(all_labels)  # [n_trials]

            # 应用预处理管道（如果启用）
            if self.enable_preprocessing and self.preprocessing_pipeline is not None:
                logger.info("应用预处理管道...")
                X_preprocessed = []

                for trial_idx in range(X.shape[0]):
                    trial_data = X[trial_idx]  # [n_channels, n_samples]

                    # 将试次数据分割为4样本的数据包进行预处理
                    processed_trial = self._apply_preprocessing_to_trial(trial_data, target_fs)
                    X_preprocessed.append(processed_trial)

                X = np.array(X_preprocessed)
                logger.info(f"预处理完成: X.shape={X.shape}")

            logger.info(f"受试者 {subject_id} 数据加载完成: X.shape={X.shape}, y.shape={y.shape}")
            logger.info(f"选择的电极: {selected_channels}")
            logger.info(f"标签分布: {np.bincount(y)}")
            logger.info(f"预处理状态: {'已应用' if self.enable_preprocessing else '未应用'}")

            return X, y
            
        except Exception as e:
            logger.error(f"加载受试者数据失败 {subject_id}: {e}")
            raise

    def _apply_preprocessing_to_trial(self, trial_data: np.ndarray, sampling_rate: int) -> np.ndarray:
        """
        对单个试次数据应用预处理管道

        Args:
            trial_data: 试次数据 [n_channels, n_samples]
            sampling_rate: 采样率

        Returns:
            预处理后的试次数据 [n_channels, n_samples]
        """
        try:
            n_channels, n_samples = trial_data.shape

            # 将试次数据分割为4样本的数据包
            packet_size = 4
            n_packets = n_samples // packet_size

            processed_packets = []

            for packet_idx in range(n_packets):
                start_idx = packet_idx * packet_size
                end_idx = start_idx + packet_size

                # 提取数据包 [n_channels, 4]
                packet_data = trial_data[:, start_idx:end_idx]

                # 应用预处理管道
                result = self.preprocessing_pipeline.process_packet(packet_data)

                # 获取最终输出数据
                if 'final_output' in result:
                    processed_packet = result['final_output']
                else:
                    # 如果预处理失败，使用原始数据
                    processed_packet = packet_data

                processed_packets.append(processed_packet)

            # 处理剩余样本（如果有）
            remaining_samples = n_samples % packet_size
            if remaining_samples > 0:
                start_idx = n_packets * packet_size
                remaining_data = trial_data[:, start_idx:]

                # 对于不足4样本的数据，进行零填充
                padded_data = np.zeros((n_channels, packet_size))
                padded_data[:, :remaining_samples] = remaining_data

                # 应用预处理
                result = self.preprocessing_pipeline.process_packet(padded_data)
                if 'final_output' in result:
                    processed_packet = result['final_output'][:, :remaining_samples]
                else:
                    processed_packet = remaining_data

                processed_packets.append(processed_packet)

            # 重新组合处理后的数据
            processed_trial = np.concatenate(processed_packets, axis=1)

            return processed_trial

        except Exception as e:
            logger.warning(f"预处理试次数据失败: {e}，返回原始数据")
            return trial_data
    
    def _load_events(self, event_file: str) -> List[Tuple[float, str]]:
        """
        加载事件文件
        
        Args:
            event_file: 事件文件路径
            
        Returns:
            List[Tuple[float, str]]: (时间戳, 事件类型) 列表
        """
        events = []
        try:
            # 尝试不同的编码格式
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            content = None

            for encoding in encodings:
                try:
                    with open(event_file, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue

            if content is None:
                logger.error(f"无法解码事件文件: {event_file}")
                return []

            for line in content.split('\n'):
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split()
                    if len(parts) >= 3:
                        timestamp = float(parts[0])
                        event_type = parts[2]
                        # 接受所有T0, T1, T2事件
                        if event_type in ['T0', 'T1', 'T2']:
                            events.append((timestamp, event_type))

            logger.debug(f"加载事件: {len(events)} 个")
            return events

        except Exception as e:
            logger.error(f"加载事件文件失败: {e}")
            return []
    
    def _extract_trials(self, raw, events: List[Tuple[float, str]], 
                       trial_duration: float, fs: int) -> Tuple[List[np.ndarray], List[int]]:
        """
        从连续EEG数据中提取试次
        
        Args:
            raw: MNE Raw对象
            events: 事件列表
            trial_duration: 试次时长（秒）
            fs: 采样率
            
        Returns:
            tuple: (试次数据列表, 标签列表)
        """
        trials = []
        labels = []
        
        data = raw.get_data()  # [n_channels, n_samples]
        n_samples_per_trial = int(trial_duration * fs)
        
        for timestamp, event_type in events:
            # 计算试次开始的样本索引
            start_sample = int(timestamp * fs)
            end_sample = start_sample + n_samples_per_trial

            # 分配标签：T0=0(平静), T1/T2=1(运动想象)
            if event_type == 'T0':
                label = 0  # 平静状态
            elif event_type in ['T1', 'T2']:
                label = 1  # 运动想象
            else:
                continue  # 跳过未知事件

            # 检查边界
            if end_sample <= data.shape[1]:
                trial_data = data[:, start_sample:end_sample]  # [n_channels, n_samples]
                trials.append(trial_data)
                labels.append(label)
        
        return trials, labels

    def _extract_trials_from_mne_events(self, raw, events, event_id,
                                       trial_duration: float, fs: int) -> Tuple[List[np.ndarray], List[int]]:
        """
        从MNE事件中提取试次

        Args:
            raw: MNE Raw对象
            events: MNE事件数组
            event_id: 事件ID映射
            trial_duration: 试次时长（秒）
            fs: 采样率

        Returns:
            tuple: (试次数据列表, 标签列表)
        """
        trials = []
        labels = []

        data = raw.get_data()  # [n_channels, n_samples]
        n_samples_per_trial = int(trial_duration * fs)

        # 查找所有相关事件：T0(休息), T1(运动想象), T2(运动想象)
        target_events = []
        for event_name, event_code in event_id.items():
            if event_name in ['T0', 'T1', 'T2']:
                target_events.append((event_code, event_name))

        logger.debug(f"目标事件: {target_events}")

        for event in events:
            event_sample, _, event_code = event

            # 检查是否为目标事件并分配标签
            event_label = None
            for target_code, target_name in target_events:
                if event_code == target_code:
                    if target_name == 'T0':
                        event_label = 0  # 平静/休息状态
                    elif target_name in ['T1', 'T2']:
                        event_label = 1  # 运动想象 (不区分双手/双脚)
                    break

            if event_label is not None:
                # 提取试次数据
                start_sample = event_sample
                end_sample = start_sample + n_samples_per_trial

                # 检查边界
                if end_sample <= data.shape[1]:
                    trial_data = data[:, start_sample:end_sample]  # [n_channels, n_samples]
                    trials.append(trial_data)
                    labels.append(event_label)

        logger.debug(f"从MNE事件提取试次: {len(trials)} 个")
        return trials, labels
    
    def load_multiple_subjects(self, subject_ids: List[str], 
                              target_fs: int = 125,
                              trial_duration: float = 3.0) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载多个受试者的数据
        
        Args:
            subject_ids: 受试者ID列表
            target_fs: 目标采样率
            trial_duration: 试次时长
            
        Returns:
            tuple: (X, y) 合并后的数据
        """
        all_X = []
        all_y = []
        
        for subject_id in subject_ids:
            try:
                X, y = self.load_subject_data(subject_id, target_fs, trial_duration)
                all_X.append(X)
                all_y.append(y)
                logger.info(f"成功加载受试者 {subject_id}")
            except Exception as e:
                logger.error(f"跳过受试者 {subject_id}: {e}")
                continue
        
        if not all_X:
            raise ValueError("未能加载任何受试者数据")
        
        # 合并数据
        X_combined = np.concatenate(all_X, axis=0)
        y_combined = np.concatenate(all_y, axis=0)
        
        logger.info(f"多受试者数据加载完成: X.shape={X_combined.shape}, y.shape={y_combined.shape}")
        logger.info(f"总标签分布: {np.bincount(y_combined)}")
        
        return X_combined, y_combined
    
    def get_available_subjects(self) -> List[str]:
        """获取可用的受试者列表"""
        subjects = []
        for item in os.listdir(self.dataset_path):
            if item.startswith('S') and os.path.isdir(os.path.join(self.dataset_path, item)):
                subjects.append(item)
        return sorted(subjects)
