"""
切空间特征提取器

基于pyRiemann库实现的切空间特征提取，专门用于运动想象EEG信号分类。
使用协方差矩阵的Riemannian几何特性，通过切空间映射提取线性特征。
这是BCI领域的经典标准方法，适合与逻辑回归分类器配合使用。
"""

import numpy as np
import logging
from typing import Optional, List, Dict, Any

try:
    from pyriemann.estimation import Covariances
    from pyriemann.tangentspace import TangentSpace
    PYRIEMANN_AVAILABLE = True
except ImportError:
    PYRIEMANN_AVAILABLE = False
    Covariances = None
    TangentSpace = None

from .base_extractor import BaseFeatureExtractor

logger = logging.getLogger(__name__)

class TangentSpaceExtractor(BaseFeatureExtractor):
    """
    切空间特征提取器
    
    基于pyRiemann库实现，将EEG信号的空间协方差矩阵通过切空间映射
    转换为欧几里得空间的线性特征。这是BCI领域的权威标准方法。
    
    算法步骤：
    1. 计算EEG信号的协方差矩阵
    2. 计算训练集协方差矩阵的黎曼均值作为参考点
    3. 将协方差矩阵映射到参考点的切空间
    4. 向量化切空间特征用于线性分类器
    """
    
    def __init__(self,
                 estimator: str = 'oas',
                 metric: str = 'riemann',
                 regularization: float = 1e-6,
                 **kwargs):
        """
        初始化切空间特征提取器

        Args:
            estimator: 协方差估计器 ('oas', 'cov', 'lwf', 'mcd', 'sch')
                      'oas' - Oracle Approximating Shrinkage (推荐，更稳定)
            metric: Riemannian度量 ('riemann', 'logeuclid', 'euclid')
                   'riemann' - 标准黎曼度量 (权威推荐)
            regularization: 协方差矩阵正则化参数，提高数值稳定性
            **kwargs: 传递给基类的参数
        """
        super().__init__(**kwargs)
        
        if not PYRIEMANN_AVAILABLE:
            raise ImportError("需要安装pyRiemann库: pip install pyriemann")
        
        self.estimator = estimator
        self.metric = metric
        self.regularization = regularization
        
        # 初始化pyRiemann组件
        self.cov_estimator = Covariances(estimator=estimator)
        self.tangent_mapper = TangentSpace(metric=metric)
        
        # 内部状态
        self._reference_matrix = None
        self._n_features = None
        
        logger.info(f"切空间特征提取器初始化完成")
        logger.info(f"  协方差估计器: {estimator}")
        logger.info(f"  黎曼度量: {metric}")
        logger.info(f"  正则化参数: {regularization}")
    
    def _fit(self, X: np.ndarray, y: np.ndarray) -> 'TangentSpaceExtractor':
        """
        训练切空间特征提取器
        
        Args:
            X: 训练数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]
            
        Returns:
            self
        """
        try:
            logger.info(f"开始训练切空间特征提取器，数据形状: {X.shape}")
            
            # 添加正则化以提高数值稳定性
            if self.regularization > 0:
                X_reg = X.copy()
                for i in range(X_reg.shape[0]):
                    # 对每个试次添加少量正则化噪声
                    noise = np.random.normal(0, self.regularization, X_reg[i].shape)
                    X_reg[i] += noise
            else:
                X_reg = X
            
            # 估计协方差矩阵
            logger.debug("正在估计协方差矩阵...")
            cov_matrices = self.cov_estimator.fit_transform(X_reg)
            logger.debug(f"协方差矩阵形状: {cov_matrices.shape}")
            
            # 训练切空间映射器
            logger.debug("正在训练切空间映射器...")
            self.tangent_mapper.fit(cov_matrices, y)
            
            # 保存参考矩阵和特征维度信息
            self._reference_matrix = self.tangent_mapper.reference_
            
            # 计算特征维度：对于n×n协方差矩阵，切空间特征维度为n(n+1)/2
            n_channels = X.shape[1]
            self._n_features = n_channels * (n_channels + 1) // 2
            
            logger.debug(f"参考矩阵形状: {self._reference_matrix.shape}")
            logger.debug(f"切空间特征维度: {self._n_features}")
            
            logger.info("切空间特征提取器训练完成")
            
            return self
            
        except Exception as e:
            logger.error(f"切空间特征提取器训练失败: {e}")
            raise
    
    def _transform(self, X: np.ndarray) -> np.ndarray:
        """
        提取切空间特征
        
        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]
            
        Returns:
            切空间特征 [n_trials, n_features]
        """
        try:
            logger.debug(f"开始切空间特征提取，数据形状: {X.shape}")
            
            # 估计协方差矩阵
            cov_matrices = self.cov_estimator.transform(X)
            logger.debug(f"协方差矩阵形状: {cov_matrices.shape}")
            
            # 切空间映射
            features = self.tangent_mapper.transform(cov_matrices)
            logger.debug(f"切空间特征形状: {features.shape}")
            
            # 验证特征维度
            if features.shape[1] != self._n_features:
                logger.warning(f"特征维度不匹配，期望{self._n_features}，实际{features.shape[1]}")
            
            logger.debug(f"切空间特征提取完成，特征维度: {features.shape[1]}")
            return features
            
        except Exception as e:
            logger.error(f"切空间特征提取失败: {e}")
            raise
    
    def get_feature_names(self) -> List[str]:
        """
        获取特征名称列表
        
        Returns:
            特征名称列表
        """
        if self._n_features is None:
            return []
        
        feature_names = []
        n_channels = self.n_channels
        
        # 生成切空间特征名称：对应协方差矩阵的上三角元素
        for i in range(n_channels):
            for j in range(i, n_channels):
                if i == j:
                    # 对角元素（方差）
                    feature_names.append(f"tangent_var_ch{i+1}")
                else:
                    # 非对角元素（协方差）
                    feature_names.append(f"tangent_cov_ch{i+1}_ch{j+1}")
        
        return feature_names
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取特征提取器信息
        
        Returns:
            包含特征提取器详细信息的字典
        """
        info = {
            'name': 'TangentSpace',
            'type': 'Riemannian Geometry',
            'estimator': self.estimator,
            'metric': self.metric,
            'regularization': self.regularization,
            'n_features': self._n_features,
            'is_fitted': self._is_fitted
        }
        
        if self._reference_matrix is not None:
            info['reference_matrix_shape'] = self._reference_matrix.shape
        
        return info
    
    def save_model(self, filepath: str) -> None:
        """
        保存训练好的模型（使用基类的标准方法）

        Args:
            filepath: 保存路径
        """
        # 使用基类的标准保存方法，保存完整对象
        super().save_model(filepath)

    def load_model(self, filepath: str) -> None:
        """
        加载训练好的模型（实例方法，用于已存在的对象）

        Args:
            filepath: 模型文件路径
        """
        # 加载完整对象并复制属性
        import pickle
        with open(filepath, 'rb') as f:
            loaded_obj = pickle.load(f)

        # 复制所有属性
        for attr_name in dir(loaded_obj):
            if not attr_name.startswith('_') or attr_name.startswith('_is_fitted') or attr_name.startswith('_reference_matrix') or attr_name.startswith('_n_features'):
                if hasattr(loaded_obj, attr_name):
                    setattr(self, attr_name, getattr(loaded_obj, attr_name))

        logger.info(f"切空间特征提取器模型已从 {filepath} 加载")
