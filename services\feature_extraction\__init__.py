"""
特征提取模块

该模块提供脑电信号的多种特征提取方法：
- FBCSP: 滤波器组共同空间模式特征提取
- Riemannian: Riemannian几何协方差矩阵特征提取
- TEF: 时域-熵域-频域特征提取
- PLV: 相位锁定值特征提取

支持通过配置文件灵活开启/关闭各种特征提取算法。
"""

from .base_extractor import BaseFeatureExtractor
from .individual_feature_manager import IndividualFeatureManager
from .config import FeatureExtractionConfig
from .plv_extractor import PLVExtractor

# 导出主要类
__all__ = [
    'BaseFeatureExtractor',
    'IndividualFeatureManager',
    'FeatureExtractionConfig',
    'PLVExtractor'
]

# 版本信息
__version__ = '1.0.0'
