"""
带通滤波器对比分析脚本

对比4-40Hz和8-30Hz带通滤波器的性能差异
分析频段优化对各类算法的具体影响
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime

def load_results(file_path: str) -> dict:
    """加载JSON结果文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"文件不存在: {file_path}")
        return {}

def extract_performance_data(results: dict, source_name: str) -> dict:
    """提取性能数据"""
    performance = {}
    
    # 算法名称映射
    algorithm_mapping = {
        'fbcsp_svm': 'FBCSP + SVM',
        'tef_rf': 'TEF + RandomForest',
        'riemannian_meanfield': 'Riemannian + MeanField',
        'tangent_space_lr': 'TangentSpace + LogisticRegression',
        'plv_svm': 'PLV + SVM'
    }
    
    # 从不同来源提取数据
    if source_name == "4-40Hz":
        # 从预处理影响测试的"有预处理"结果中提取
        if 'with_preprocessing_results' in results and 'algorithms' in results['with_preprocessing_results']:
            algorithms = results['with_preprocessing_results']['algorithms']
        else:
            return {}
    elif source_name == "8-30Hz":
        # 从带通优化测试结果中提取
        if 'optimized_results' in results and 'algorithms' in results['optimized_results']:
            algorithms = results['optimized_results']['algorithms']
        else:
            return {}
    else:
        return {}
    
    for alg_key, alg_name in algorithm_mapping.items():
        if alg_key in algorithms:
            alg_data = algorithms[alg_key]
            mean_acc = alg_data.get('mean_cv_accuracy', 0)
            std_acc = alg_data.get('std_cv_accuracy', 0)
            performance[alg_name] = {'accuracy': mean_acc, 'std': std_acc}
    
    return performance

def generate_comparison_analysis():
    """生成对比分析报告"""
    
    # 加载结果数据
    preprocessing_results = load_results("preprocessing_impact_comparison_report.json")
    bandpass_results = load_results("bandpass_optimization_report.json")
    
    # 提取性能数据
    performance_4_40 = extract_performance_data(preprocessing_results, "4-40Hz")
    performance_8_30 = extract_performance_data(bandpass_results, "8-30Hz")
    
    if not performance_4_40 or not performance_8_30:
        print("无法加载完整的性能数据")
        return
    
    # 生成对比分析
    analysis_lines = []
    
    # 报告头部
    analysis_lines.append("带通滤波器频段对比分析报告")
    analysis_lines.append("=" * 60)
    analysis_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    analysis_lines.append("对比内容: 4-40Hz vs 8-30Hz 带通滤波器性能")
    analysis_lines.append("测试数据: 前3名受试者，5类算法")
    analysis_lines.append("")
    
    # 详细对比表格
    analysis_lines.append("详细性能对比表")
    analysis_lines.append("-" * 80)
    analysis_lines.append(f"{'算法名称':<30} {'4-40Hz':<15} {'8-30Hz':<15} {'改进幅度':<15}")
    analysis_lines.append("-" * 80)
    
    improvements = []
    
    for alg_name in performance_4_40.keys():
        if alg_name in performance_8_30:
            acc_4_40 = performance_4_40[alg_name]['accuracy']
            acc_8_30 = performance_8_30[alg_name]['accuracy']
            
            improvement = acc_8_30 - acc_4_40
            improvement_percent = (improvement / acc_4_40 * 100) if acc_4_40 > 0 else 0
            
            improvements.append(improvement)
            
            # 格式化显示
            acc_4_40_str = f"{acc_4_40:.3f}"
            acc_8_30_str = f"{acc_8_30:.3f}"
            
            if improvement >= 0:
                improvement_str = f"+{improvement:.3f} ({improvement_percent:+.1f}%)"
            else:
                improvement_str = f"{improvement:.3f} ({improvement_percent:+.1f}%)"
            
            analysis_lines.append(f"{alg_name:<30} {acc_4_40_str:<15} {acc_8_30_str:<15} {improvement_str:<15}")
    
    analysis_lines.append("-" * 80)
    
    # 总体分析
    if improvements:
        avg_improvement = np.mean(improvements)
        improved_count = sum(1 for imp in improvements if imp > 0)
        degraded_count = sum(1 for imp in improvements if imp < 0)
        
        analysis_lines.append("")
        analysis_lines.append("总体优化效果分析")
        analysis_lines.append("=" * 40)
        analysis_lines.append(f"平均性能提升: {avg_improvement:.3f}")
        analysis_lines.append(f"性能提升算法: {improved_count}/5")
        analysis_lines.append(f"性能下降算法: {degraded_count}/5")
        
        if avg_improvement > 0:
            analysis_lines.append("✅ 总体优化效果: 正面")
        else:
            analysis_lines.append("❌ 总体优化效果: 负面")
    
    # 具体算法分析
    analysis_lines.append("")
    analysis_lines.append("具体算法优化分析")
    analysis_lines.append("=" * 40)
    
    for alg_name in performance_4_40.keys():
        if alg_name in performance_8_30:
            acc_4_40 = performance_4_40[alg_name]['accuracy']
            acc_8_30 = performance_8_30[alg_name]['accuracy']
            improvement = acc_8_30 - acc_4_40
            improvement_percent = (improvement / acc_4_40 * 100) if acc_4_40 > 0 else 0
            
            analysis_lines.append(f"\n📊 {alg_name}:")
            analysis_lines.append(f"   4-40Hz: {acc_4_40:.3f}")
            analysis_lines.append(f"   8-30Hz: {acc_8_30:.3f}")
            analysis_lines.append(f"   变化: {improvement:+.3f} ({improvement_percent:+.1f}%)")
            
            # 分析原因
            if "FBCSP" in alg_name:
                if improvement > 0:
                    analysis_lines.append("   ✅ 符合预期：FBCSP专门针对μ/β节律，8-30Hz更精确")
                else:
                    analysis_lines.append("   ⚠️  低于预期：可能需要调整FBCSP频段配置")
            elif "TEF" in alg_name:
                analysis_lines.append("   📝 时域特征对频段变化敏感度中等")
            elif "Riemannian" in alg_name:
                if improvement > 0:
                    analysis_lines.append("   ✅ 符合预期：协方差矩阵受频段纯净度影响大")
                else:
                    analysis_lines.append("   ⚠️  需要关注：可能存在频段适应性问题")
            elif "TangentSpace" in alg_name:
                if improvement > 0:
                    analysis_lines.append("   ✅ 符合预期：切空间特征对频段选择敏感")
                else:
                    analysis_lines.append("   ⚠️  需要调整：切空间映射可能需要重新优化")
            elif "PLV" in alg_name:
                analysis_lines.append("   📝 相位锁定值在μ/β节律表现中等")
    
    # 频段优化理论分析
    analysis_lines.append("")
    analysis_lines.append("频段优化理论分析")
    analysis_lines.append("=" * 40)
    analysis_lines.append("🔬 4-40Hz → 8-30Hz 的理论优势:")
    analysis_lines.append("   • 移除4-8Hz: 减少低频漂移和眼动伪迹")
    analysis_lines.append("   • 聚焦8-12Hz: 增强μ节律运动想象特征")
    analysis_lines.append("   • 保留13-30Hz: 保持β节律运动执行信息")
    analysis_lines.append("   • 移除30-40Hz: 减少肌电干扰和高频噪声")
    analysis_lines.append("")
    analysis_lines.append("🎯 运动想象BCI最优频段:")
    analysis_lines.append("   • μ节律 (8-12Hz): 运动想象时功率下降")
    analysis_lines.append("   • β节律 (13-30Hz): 运动准备和执行相关")
    analysis_lines.append("   • 避免γ节律 (>30Hz): 主要是肌电干扰")
    
    # 建议
    analysis_lines.append("")
    analysis_lines.append("优化建议")
    analysis_lines.append("=" * 20)
    
    if avg_improvement > 0:
        analysis_lines.append("✅ 建议采用8-30Hz带通滤波器")
        analysis_lines.append("   理由：总体性能提升，更符合运动想象BCI理论")
    else:
        analysis_lines.append("⚠️  需要进一步分析")
        analysis_lines.append("   建议：检查特征提取参数是否需要相应调整")
    
    analysis_lines.append("")
    analysis_lines.append("🔧 后续优化方向:")
    analysis_lines.append("   1. 调整FBCSP频段配置以匹配8-30Hz")
    analysis_lines.append("   2. 优化TEF特征提取的频域参数")
    analysis_lines.append("   3. 重新校准Riemannian和TangentSpace参数")
    analysis_lines.append("   4. 考虑PLV算法的频段特异性优化")
    
    # 保存分析报告
    report_content = "\n".join(analysis_lines)
    
    with open("bandpass_comparison_analysis.txt", 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("带通滤波器对比分析报告已生成: bandpass_comparison_analysis.txt")
    print("\n" + report_content)

if __name__ == "__main__":
    generate_comparison_analysis()
