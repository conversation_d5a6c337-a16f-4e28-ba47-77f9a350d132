# -*- coding: utf-8 -*-
"""
特征选择工具
Feature Selection Tools

实现多种特征选择算法，特别针对小样本EEG数据优化
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Optional, Union
from sklearn.feature_selection import mutual_info_classif, SelectKBest, f_classif
from sklearn.preprocessing import StandardScaler
import logging

logger = logging.getLogger(__name__)


class mRMRSelector:
    """
    最大相关最小冗余特征选择器
    Maximum Relevance Minimum Redundancy Feature Selector
    
    专门针对小样本EEG数据设计，解决过拟合问题
    """
    
    def __init__(self, max_features: int = 8, relevance_method: str = 'mutual_info'):
        """
        初始化mRMR选择器
        
        Args:
            max_features: 最大特征数量
            relevance_method: 相关性计算方法 ('mutual_info', 'f_score')
        """
        self.max_features = max_features
        self.relevance_method = relevance_method
        self.selected_features_ = None
        self.feature_scores_ = None
        
    def _calculate_relevance(self, X: np.ndarray, y: np.ndarray) -> np.ndarray:
        """计算特征与目标的相关性"""
        if self.relevance_method == 'mutual_info':
            return mutual_info_classif(X, y, random_state=42)
        elif self.relevance_method == 'f_score':
            f_scores, _ = f_classif(X, y)
            return f_scores
        else:
            raise ValueError(f"不支持的相关性方法: {self.relevance_method}")
    
    def _calculate_redundancy(self, X: np.ndarray, selected_idx: List[int],
                            candidate_idx: int) -> float:
        """计算候选特征与已选特征的冗余度"""
        if not selected_idx:
            return 0.0

        candidate_feature = X[:, candidate_idx]
        redundancies = []

        for idx in selected_idx:
            selected_feature = X[:, idx]
            # 使用相关系数计算冗余度（更稳定）
            correlation = np.corrcoef(candidate_feature, selected_feature)[0, 1]
            redundancy = abs(correlation)  # 使用绝对相关系数
            redundancies.append(redundancy)

        return np.mean(redundancies)
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'mRMRSelector':
        """
        训练mRMR选择器
        
        Args:
            X: 特征矩阵 (n_samples, n_features)
            y: 目标标签 (n_samples,)
        """
        try:
            # 标准化特征
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # 计算所有特征的相关性
            relevance_scores = self._calculate_relevance(X_scaled, y)
            
            # mRMR选择过程
            selected_features = []
            remaining_features = list(range(X.shape[1]))
            feature_scores = []
            
            for _ in range(min(self.max_features, X.shape[1])):
                best_score = -np.inf
                best_feature = None
                
                for candidate in remaining_features:
                    # 计算相关性
                    relevance = relevance_scores[candidate]
                    
                    # 计算冗余度
                    redundancy = self._calculate_redundancy(X_scaled, selected_features, candidate)
                    
                    # mRMR评分：最大化相关性，最小化冗余度
                    score = relevance - redundancy
                    
                    if score > best_score:
                        best_score = score
                        best_feature = candidate
                
                if best_feature is not None:
                    selected_features.append(best_feature)
                    remaining_features.remove(best_feature)
                    feature_scores.append(best_score)
            
            self.selected_features_ = np.array(selected_features)
            self.feature_scores_ = np.array(feature_scores)
            
            logger.info(f"mRMR特征选择完成，从{X.shape[1]}个特征中选择了{len(selected_features)}个")
            
            return self
            
        except Exception as e:
            logger.error(f"mRMR特征选择失败: {e}")
            # 回退到简单的方差选择
            selector = SelectKBest(f_classif, k=self.max_features)
            selector.fit(X, y)
            self.selected_features_ = selector.get_support(indices=True)
            self.feature_scores_ = selector.scores_[self.selected_features_]
            return self
    
    def transform(self, X: np.ndarray) -> np.ndarray:
        """
        应用特征选择
        
        Args:
            X: 输入特征矩阵
            
        Returns:
            选择后的特征矩阵
        """
        if self.selected_features_ is None:
            raise ValueError("选择器尚未训练，请先调用fit方法")
        
        return X[:, self.selected_features_]
    
    def fit_transform(self, X: np.ndarray, y: np.ndarray) -> np.ndarray:
        """训练并应用特征选择"""
        return self.fit(X, y).transform(X)
    
    def get_selected_features(self) -> np.ndarray:
        """获取选择的特征索引"""
        return self.selected_features_
    
    def get_feature_scores(self) -> np.ndarray:
        """获取特征评分"""
        return self.feature_scores_


class ChannelSelector:
    """
    通道选择器
    专门用于EEG数据的通道选择
    """
    
    def __init__(self, selected_channels: List[int]):
        """
        初始化通道选择器
        
        Args:
            selected_channels: 选择的通道索引列表（0-based）
        """
        self.selected_channels = np.array(selected_channels)
    
    def apply_channel_selection(self, features: np.ndarray, 
                              n_channels: int = 8) -> np.ndarray:
        """
        应用通道选择到特征矩阵
        
        Args:
            features: 原始特征矩阵
            n_channels: 总通道数
            
        Returns:
            选择后的特征矩阵
        """
        if features.shape[1] % n_channels != 0:
            logger.warning(f"特征数量{features.shape[1]}不能被通道数{n_channels}整除")
            return features
        
        features_per_channel = features.shape[1] // n_channels
        selected_features = []
        
        for channel in self.selected_channels:
            start_idx = channel * features_per_channel
            end_idx = (channel + 1) * features_per_channel
            selected_features.extend(range(start_idx, end_idx))
        
        return features[:, selected_features]


def create_feature_selector(config: dict) -> Optional[object]:
    """
    根据配置创建特征选择器
    
    Args:
        config: 特征选择配置
        
    Returns:
        特征选择器实例
    """
    if not config.get('enabled', False):
        return None
    
    method = config.get('method', 'mrmr')
    
    if method == 'mrmr':
        return mRMRSelector(
            max_features=config.get('max_features', 8),
            relevance_method=config.get('relevance_method', 'mutual_info')
        )
    else:
        logger.warning(f"不支持的特征选择方法: {method}")
        return None
