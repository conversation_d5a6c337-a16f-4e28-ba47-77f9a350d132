#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加权投票分类器系统

实现多分类器加权投票，支持难度等级和触发阈值控制

作者: AI Assistant
创建时间: 2025-01-08
"""

import numpy as np
import logging
import json
import pickle
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import accuracy_score
from .temporal_smoothing import TemporalSmoother, SmoothingResult

logger = logging.getLogger(__name__)


@dataclass
class ClassifierPerformance:
    """分类器性能信息"""
    accuracy: float
    cv_scores: List[float]
    cv_mean: float
    cv_std: float
    training_time: float
    n_samples: int


@dataclass
class DifficultyLevel:
    """难度等级配置"""
    level: int  # 1-5级
    name: str
    trigger_threshold: float  # 触发阈值
    description: str


@dataclass
class ClassificationResult:
    """分类结果详细信息"""
    final_prediction: int
    final_probability: np.ndarray
    confidence: float
    individual_predictions: Dict[str, int]
    individual_probabilities: Dict[str, np.ndarray]
    voting_weights: Dict[str, float]
    trigger_decision: bool
    trigger_threshold: float
    difficulty_level: int
    agreement_level: str


class WeightedVotingClassifierSystem:
    """加权投票分类器系统 - 支持Stacking元学习器"""

    def __init__(self):
        self.classifiers = {}
        self.feature_extractors = {}
        self.performance = {}
        self.voting_weights = {}
        self.is_trained = False

        # Stacking相关（针对低SNR环境优化）
        self.use_stacking = True  # 默认使用Stacking
        self.meta_classifier = None
        self.selected_classifiers = {}  # 筛选后的分类器
        self.performance_threshold = 0.45  # 性能阈值（进一步降低以包含更多分类器）
        self.use_simple_averaging = True  # 是否使用简单平均而不是复杂元学习器
        self.regularization_strength = "medium"  # 正则化强度（增强以提高稳定性）

        # 难度等级配置（针对低SNR环境优化阈值）
        self.difficulty_levels = {
            1: DifficultyLevel(1, "简单", 0.50, "极低阈值，最容易触发"),
            2: DifficultyLevel(2, "较简单", 0.55, "低阈值，容易触发"),
            3: DifficultyLevel(3, "中等", 0.60, "中低阈值，适中触发（从0.75降低）"),
            4: DifficultyLevel(4, "较困难", 0.70, "中等阈值，需要集中注意力"),
            5: DifficultyLevel(5, "困难", 0.80, "较高阈值，需要强烈运动想象")
        }

        # 当前设置
        self.current_difficulty = 3  # 默认中等难度
        self.custom_trigger_threshold = None  # 自定义触发阈值

        # 权重计算方法
        self.weight_method = "stacking"  # "performance", "equal", "custom", "stacking"
        self.custom_weights = {}

        # 时序平滑器（针对低SNR环境增强稳定性）
        self.temporal_smoother = TemporalSmoother(
            window_size=5,
            smoothing_method="voting",  # 默认使用投票方法
            confidence_threshold=0.6,
            alpha=0.3
        )
        self.enable_temporal_smoothing = False  # 🔧 关闭时序平滑：避免双重平滑导致响应迟钝
        
        # 调试输出控制
        self.enable_realtime_debug = True  # 是否启用实时调试输出

        # 🔧 动态权重管理器（2024年最新研究）
        self.dynamic_weight_manager = None
        self.enable_dynamic_weights = False  # 默认关闭，需要配置启用

        # 🔧 分类器输出平滑器（基于EEG BCI研究）
        self.classifier_smoother = None
        self.enable_classifier_smoothing = False  # 默认关闭，需要配置启用
    
    def add_classifier(self, name: str, classifier, feature_extractor, performance: ClassifierPerformance):
        """添加分类器"""
        self.classifiers[name] = classifier
        self.feature_extractors[name] = feature_extractor
        self.performance[name] = performance

        logger.info(f"添加分类器: {name}, 性能: {performance.cv_mean:.3f}±{performance.cv_std:.3f}")

    def initialize_dynamic_weights(self, config: dict = None) -> None:
        """
        初始化动态权重管理器

        Args:
            config: 动态权重配置
        """
        try:
            if not self.classifiers:
                logger.warning("没有分类器，无法初始化动态权重管理器")
                return

            from .dynamic_weight_manager import DynamicWeightManager

            # 默认配置
            default_config = {
                'window_size': 20,
                'update_frequency': 10,
                'temperature': 2.0,
                'min_weight': 0.05,
                'performance_decay': 0.95
            }

            if config:
                default_config.update(config)

            # 创建动态权重管理器
            classifier_names = list(self.classifiers.keys())
            self.dynamic_weight_manager = DynamicWeightManager(
                classifier_names=classifier_names,
                **default_config
            )

            self.enable_dynamic_weights = True
            logger.info(f"动态权重管理器初始化完成，管理{len(classifier_names)}个分类器")

        except Exception as e:
            logger.error(f"动态权重管理器初始化失败: {e}")
            self.enable_dynamic_weights = False

    def initialize_classifier_smoothing(self, config: dict = None) -> None:
        """
        初始化分类器输出平滑器

        Args:
            config: 平滑器配置
        """
        try:
            if not self.classifiers:
                logger.warning("没有分类器，无法初始化平滑器")
                return

            from .classifier_smoother import create_classifier_smoother

            # 默认配置：主要针对FBCSP进行平滑
            default_config = {
                'fbcsp_svm': {'enabled': True, 'alpha': 0.3},
                'riemannian_meanfield': {'enabled': False, 'alpha': 0.2},
                'tangent_space_lr': {'enabled': True, 'alpha': 0.2},
                'plv_svm': {'enabled': False, 'alpha': 0.1},
                'tef_rf': {'enabled': True, 'alpha': 0.25}
            }

            if config:
                default_config.update(config)

            # 创建分类器平滑器
            self.classifier_smoother = create_classifier_smoother(default_config)
            self.enable_classifier_smoothing = True

            # 显示平滑器状态
            status = self.classifier_smoother.get_smoother_status()
            enabled_smoothers = [name for name, info in status.items() if info['enabled']]
            logger.info(f"分类器平滑器初始化完成，启用: {enabled_smoothers}")

        except Exception as e:
            logger.error(f"分类器平滑器初始化失败: {e}")
            self.enable_classifier_smoothing = False

    def _select_best_classifiers(self):
        """基于性能筛选最佳分类器组合"""
        if not self.performance:
            return

        # 按性能排序
        sorted_classifiers = sorted(
            self.performance.items(),
            key=lambda x: x[1].cv_mean,
            reverse=True
        )

        # 筛选策略：排除性能低于阈值的分类器
        selected = {}
        excluded = []

        for name, perf in sorted_classifiers:
            if perf.cv_mean >= self.performance_threshold:
                selected[name] = {
                    'classifier': self.classifiers[name],
                    'feature_extractor': self.feature_extractors[name],
                    'performance': perf
                }
            else:
                excluded.append(name)

        # 确保至少保留2个分类器
        if len(selected) < 2:
            logger.warning(f"筛选后分类器数量不足({len(selected)})，保留前3个最佳分类器")
            selected = {}
            # 确保至少有分类器可选
            classifiers_to_select = min(3, len(sorted_classifiers))
            for name, perf in sorted_classifiers[:classifiers_to_select]:
                selected[name] = {
                    'classifier': self.classifiers[name],
                    'feature_extractor': self.feature_extractors[name],
                    'performance': perf
                }
            logger.info(f"强制保留了{len(selected)}个分类器，性能范围: {[f'{name}:{perf.cv_mean:.3f}' for name, perf in sorted_classifiers[:classifiers_to_select]]}")

        self.selected_classifiers = selected

        logger.info(f"分类器筛选完成:")
        logger.info(f"  保留: {list(selected.keys())}")
        if excluded:
            logger.info(f"  排除: {excluded}")

        return selected

    def train_stacking_ensemble(self, X: np.ndarray, y: np.ndarray):
        """训练Stacking集成模型"""
        try:
            # 首先筛选最佳分类器
            selected = self._select_best_classifiers()

            if len(selected) < 2:
                logger.error("可用分类器数量不足，无法训练Stacking模型")
                return False

            # 检查是否使用简单平均
            if self.use_simple_averaging:
                logger.info("使用简单平均模式，跳过复杂元学习器训练...")
                self.meta_classifier = "simple_averaging"
                self.is_trained = True
                self.weight_method = "stacking"
                logger.info("简单平均Stacking系统训练完成")
                return True

            logger.info("开始训练Stacking元学习器...")

            # 生成元特征（使用交叉验证避免过拟合）
            meta_features = self._generate_meta_features(X, y, selected)

            if meta_features is None:
                logger.error("元特征生成失败")
                return False

            # 同时测试简单平均作为对比
            simple_avg_scores = self._evaluate_simple_averaging(meta_features, y)

            if self.use_simple_averaging:
                # 使用简单平均
                self.meta_classifier = "simple_averaging"  # 特殊标记
                cv_scores = simple_avg_scores
                logger.info(f"使用简单平均方法")
                logger.info(f"简单平均CV性能: {np.mean(cv_scores):.3f}±{np.std(cv_scores):.3f}")
            else:
                # 训练正则化元分类器（防止小数据集过拟合）
                self.meta_classifier = self._train_regularized_meta_learner(meta_features, y)

                # 验证元分类器性能
                cv_scores = cross_val_score(
                    self.meta_classifier, meta_features, y,
                    cv=StratifiedKFold(n_splits=3, shuffle=True, random_state=42),
                    scoring='accuracy'
                )

                logger.info(f"正则化Stacking元学习器训练完成")
                logger.info(f"元学习器CV性能: {np.mean(cv_scores):.3f}±{np.std(cv_scores):.3f}")
                logger.info(f"简单平均CV性能: {np.mean(simple_avg_scores):.3f}±{np.std(simple_avg_scores):.3f}")
                logger.info(f"使用的正则化配置: {self.meta_classifier.get_params()}")

                # 如果简单平均明显更好，给出建议
                if np.mean(simple_avg_scores) > np.mean(cv_scores) + 0.05:
                    logger.warning("简单平均性能明显优于正则化元学习器，建议考虑使用简单平均")

            self.is_trained = True
            self.weight_method = "stacking"

            return True

        except Exception as e:
            logger.error(f"Stacking训练失败: {e}")
            return False

    def _train_regularized_meta_learner(self, meta_features: np.ndarray, y: np.ndarray):
        """训练正则化元学习器（防止小数据集过拟合）"""
        try:
            from sklearn.model_selection import GridSearchCV

            n_samples, n_features = meta_features.shape
            logger.info(f"训练正则化元学习器: 样本数={n_samples}, 特征数={n_features}")

            # 根据数据集大小选择正则化策略
            if n_samples < 100:  # 小数据集，使用强正则化
                logger.info("检测到小数据集，使用强正则化策略")

                # 定义候选的正则化配置
                param_grid = [
                    {
                        'penalty': ['l2'],
                        'C': [0.001, 0.01, 0.1, 1.0],  # 强到弱的正则化
                        'solver': ['lbfgs'],
                        'max_iter': [1000],
                        'class_weight': ['balanced']
                    },
                    {
                        'penalty': ['l1'],
                        'C': [0.01, 0.1, 1.0],  # L1正则化用于特征选择
                        'solver': ['liblinear'],
                        'max_iter': [1000],
                        'class_weight': ['balanced']
                    }
                ]

                # 使用网格搜索找到最佳正则化参数
                base_classifier = LogisticRegression(random_state=42)

                # 使用较少的CV折数（适合小数据集）
                cv_folds = min(3, len(np.unique(y)))

                grid_search = GridSearchCV(
                    base_classifier,
                    param_grid,
                    cv=StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42),
                    scoring='accuracy',
                    n_jobs=1,  # 避免并行问题
                    verbose=0
                )

                grid_search.fit(meta_features, y)

                best_classifier = grid_search.best_estimator_
                best_params = grid_search.best_params_
                best_score = grid_search.best_score_

                logger.info(f"最佳正则化参数: {best_params}")
                logger.info(f"最佳CV分数: {best_score:.3f}")

                return best_classifier

            else:  # 较大数据集，使用中等正则化
                logger.info("使用中等正则化策略")

                return LogisticRegression(
                    random_state=42,
                    max_iter=1000,
                    class_weight='balanced',
                    penalty='l2',
                    C=0.1,  # 中等强度正则化
                    solver='lbfgs'
                )

        except Exception as e:
            logger.warning(f"正则化元学习器训练失败，回退到默认配置: {e}")

            # 回退到保守的正则化配置
            return LogisticRegression(
                random_state=42,
                max_iter=1000,
                class_weight='balanced',
                penalty='l2',
                C=0.01,  # 强正则化作为安全回退
                solver='lbfgs'
            )

    def _evaluate_simple_averaging(self, meta_features: np.ndarray, y: np.ndarray) -> np.ndarray:
        """评估简单平均方法的性能"""
        try:
            from sklearn.metrics import accuracy_score

            cv_scores = []
            skf = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)

            n_classifiers = len(self.selected_classifiers)

            for train_idx, val_idx in skf.split(meta_features, y):
                meta_val = meta_features[val_idx]
                y_val = y[val_idx]

                # 简单平均：对每个分类器的概率进行平均
                avg_proba = np.zeros((len(meta_val), 2))

                for i in range(n_classifiers):
                    start_idx = i * 2
                    end_idx = start_idx + 2
                    avg_proba += meta_val[:, start_idx:end_idx]

                avg_proba /= n_classifiers

                # 预测
                predictions = np.argmax(avg_proba, axis=1)
                accuracy = accuracy_score(y_val, predictions)
                cv_scores.append(accuracy)

            return np.array(cv_scores)

        except Exception as e:
            logger.warning(f"简单平均评估失败: {e}")
            return np.array([0.5, 0.5, 0.5])  # 返回默认分数

    def _generate_meta_features(self, X: np.ndarray, y: np.ndarray, selected_classifiers: Dict) -> Optional[np.ndarray]:
        """生成元特征（使用交叉验证）"""
        try:
            n_samples = len(y)
            n_classifiers = len(selected_classifiers)

            # 元特征矩阵：每个样本对应每个分类器的预测概率
            meta_features = np.zeros((n_samples, n_classifiers * 2))  # 2类概率

            # 使用3折交叉验证生成元特征
            skf = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)

            for train_idx, val_idx in skf.split(X, y):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train = y[train_idx]

                # 训练每个基分类器并在验证集上预测
                for i, (name, data) in enumerate(selected_classifiers.items()):
                    try:
                        # 复制分类器和特征提取器
                        classifier = data['classifier'].__class__(**data['classifier'].get_params())
                        feature_extractor = data['feature_extractor']

                        # 训练特征提取器
                        temp_extractor = feature_extractor.__class__(**feature_extractor.get_params())
                        temp_extractor.fit(X_train, y_train)

                        # 提取特征
                        train_features = temp_extractor.transform(X_train)
                        val_features = temp_extractor.transform(X_val)

                        # 特殊处理Riemannian分类器
                        if 'riemannian' in name.lower():
                            # 使用协方差矩阵
                            from pyriemann.estimation import Covariances
                            cov_estimator = Covariances(estimator='oas')
                            train_features = cov_estimator.fit_transform(X_train)
                            val_features = cov_estimator.transform(X_val)

                        # 训练分类器
                        classifier.fit(train_features, y_train)

                        # 预测验证集
                        if hasattr(classifier, 'predict_proba'):
                            val_proba = classifier.predict_proba(val_features)
                        else:
                            # 对于没有概率输出的分类器，使用决策函数或硬预测
                            val_pred = classifier.predict(val_features)
                            val_proba = np.zeros((len(val_pred), 2))
                            val_proba[val_pred == 0, 0] = 0.9
                            val_proba[val_pred == 0, 1] = 0.1
                            val_proba[val_pred == 1, 0] = 0.1
                            val_proba[val_pred == 1, 1] = 0.9

                        # 存储元特征
                        meta_features[val_idx, i*2:(i+1)*2] = val_proba

                    except Exception as e:
                        logger.warning(f"分类器 {name} 元特征生成失败: {e}")
                        # 使用随机概率作为默认值
                        meta_features[val_idx, i*2:(i+1)*2] = np.random.rand(len(val_idx), 2)
                        meta_features[val_idx, i*2:(i+1)*2] /= meta_features[val_idx, i*2:(i+1)*2].sum(axis=1, keepdims=True)

            logger.info(f"元特征生成完成，形状: {meta_features.shape}")
            return meta_features

        except Exception as e:
            logger.error(f"元特征生成失败: {e}")
            return None
    
    def _update_voting_weights(self):
        """更新投票权重"""
        if not self.performance:
            return
        
        if self.weight_method == "performance":
            # 基于交叉验证性能计算权重
            total_score = sum(perf.cv_mean for perf in self.performance.values())
            
            if total_score > 0:
                self.voting_weights = {
                    name: perf.cv_mean / total_score 
                    for name, perf in self.performance.items()
                }
            else:
                # 如果所有性能都为0，使用均等权重
                n_classifiers = len(self.performance)
                self.voting_weights = {
                    name: 1.0 / n_classifiers 
                    for name in self.performance.keys()
                }
        
        elif self.weight_method == "equal":
            # 均等权重
            n_classifiers = len(self.performance)
            self.voting_weights = {
                name: 1.0 / n_classifiers 
                for name in self.performance.keys()
            }
        
        elif self.weight_method == "custom":
            # 使用自定义权重
            if self.custom_weights:
                # 归一化自定义权重
                total_weight = sum(self.custom_weights.values())
                self.voting_weights = {
                    name: weight / total_weight 
                    for name, weight in self.custom_weights.items()
                    if name in self.performance
                }
            else:
                # 回退到均等权重
                n_classifiers = len(self.performance)
                self.voting_weights = {
                    name: 1.0 / n_classifiers 
                    for name in self.performance.keys()
                }
        
        logger.info(f"更新投票权重 ({self.weight_method}): {self.voting_weights}")
    
    def set_difficulty_level(self, level: int):
        """设置难度等级"""
        if level in self.difficulty_levels:
            self.current_difficulty = level
            # 清除自定义触发阈值，使用难度等级的默认阈值
            self.custom_trigger_threshold = None
            logger.info(f"设置难度等级为: {level} - {self.difficulty_levels[level].name}")
            logger.info(f"触发阈值重置为难度等级默认值: {self.difficulty_levels[level].trigger_threshold}")
        else:
            raise ValueError(f"无效的难度等级: {level}")
    
    def set_custom_thresholds(self, trigger_threshold: float = None):
        """设置自定义触发阈值"""
        if trigger_threshold is not None:
            if 0.5 <= trigger_threshold <= 1.0:
                self.custom_trigger_threshold = trigger_threshold
            else:
                raise ValueError("触发阈值必须在0.5-1.0之间")

        logger.info(f"设置自定义触发阈值: {trigger_threshold}")
    
    def set_realtime_debug(self, enabled: bool = True):
        """设置实时调试输出开关
        
        Args:
            enabled: True启用实时调试输出，False关闭
        """
        self.enable_realtime_debug = enabled
        logger.info(f"实时调试输出: {'启用' if enabled else '关闭'}")
    
    def set_weight_method(self, method: str, custom_weights: Dict[str, float] = None):
        """设置权重计算方法"""
        if method not in ["performance", "equal", "custom", "stacking"]:
            raise ValueError("权重方法必须是 'performance', 'equal', 'custom', 或 'stacking'")

        self.weight_method = method

        if method == "custom" and custom_weights:
            self.custom_weights = custom_weights.copy()

        # Stacking方法有自己的权重逻辑，不需要调用_update_voting_weights
        if method != "stacking":
            self._update_voting_weights()
    
    def get_current_thresholds(self) -> float:
        """获取当前触发阈值"""
        if self.custom_trigger_threshold is not None:
            return self.custom_trigger_threshold
        else:
            difficulty = self.difficulty_levels[self.current_difficulty]
            return difficulty.trigger_threshold
    
    def get_difficulty_info(self) -> DifficultyLevel:
        """获取当前难度等级信息"""
        return self.difficulty_levels[self.current_difficulty]

    def set_temporal_smoothing(self, enabled: bool, method: str = "voting", window_size: int = 5):
        """设置时序平滑参数"""
        self.enable_temporal_smoothing = enabled
        if enabled:
            self.temporal_smoother = TemporalSmoother(
                window_size=window_size,
                smoothing_method=method,
                confidence_threshold=0.6,
                alpha=0.3
            )
            logger.info(f"时序平滑已启用: 方法={method}, 窗口大小={window_size}")
        else:
            logger.info("时序平滑已禁用")

    def reset_temporal_smoother(self):
        """重置时序平滑器状态"""
        if hasattr(self, 'temporal_smoother'):
            self.temporal_smoother.reset()
            logger.info("时序平滑器状态已重置")
    
    def predict(self, X: np.ndarray) -> ClassificationResult:
        """预测（仅支持Stacking高性能系统）"""
        if not self.is_trained:
            raise ValueError("分类器系统未训练")

        if self.weight_method == "stacking" and self.meta_classifier is not None:
            return self._predict_stacking(X)
        else:
            raise ValueError("系统仅支持Stacking高性能分类，请重新训练模型以获得Stacking支持")

    def _predict_stacking(self, X: np.ndarray) -> ClassificationResult:
        """Stacking预测"""
        individual_predictions = {}
        individual_probabilities = {}
        valid_classifiers = []

        # 获取基分类器预测（用于元特征）
        meta_features = []

        for name, data in self.selected_classifiers.items():
            try:
                classifier = data['classifier']
                feature_extractor = data['feature_extractor']

                # 提取特征
                features = feature_extractor.transform(X)

                # 特殊处理Riemannian分类器
                if 'riemannian' in name.lower():
                    from pyriemann.estimation import Covariances
                    cov_estimator = Covariances(estimator='oas')
                    features = cov_estimator.fit_transform(X)

                # 预测
                if hasattr(classifier, 'predict_proba'):
                    proba = classifier.predict_proba(features)[0]

                    # 🔧 应用分类器平滑（如果启用）
                    if self.enable_classifier_smoothing and self.classifier_smoother:
                        proba = self.classifier_smoother.smooth_classifier_output(name, proba)

                    pred = np.argmax(proba)
                    individual_probabilities[name] = proba
                    meta_features.extend(proba)  # 添加到元特征
                else:
                    pred = classifier.predict(features)[0]
                    proba = np.array([0.1, 0.9]) if pred == 1 else np.array([0.9, 0.1])

                    # 🔧 应用分类器平滑（如果启用）
                    if self.enable_classifier_smoothing and self.classifier_smoother:
                        proba = self.classifier_smoother.smooth_classifier_output(name, proba)

                    individual_probabilities[name] = proba
                    meta_features.extend(proba)

                individual_predictions[name] = pred
                valid_classifiers.append(name)

            except Exception as e:
                logger.warning(f"{name}分类器预测失败: {e}")
                continue

        if not valid_classifiers:
            raise ValueError("所有分类器预测失败")

        # 使用元学习器或简单平均进行最终预测
        meta_features = np.array(meta_features).reshape(1, -1)

        try:
            if self.meta_classifier == "simple_averaging":
                # 🔧 使用动态权重或简单平均
                if self.enable_dynamic_weights and self.dynamic_weight_manager:
                    # 使用动态权重
                    current_weights = self.dynamic_weight_manager.get_current_weights()
                    final_probability = np.zeros(2)
                    total_weight = 0.0

                    for i, classifier_name in enumerate(valid_classifiers):
                        start_idx = i * 2
                        end_idx = start_idx + 2
                        weight = current_weights.get(classifier_name, 1.0 / len(valid_classifiers))
                        final_probability += weight * meta_features[0, start_idx:end_idx]
                        total_weight += weight

                    if total_weight > 0:
                        final_probability /= total_weight
                    else:
                        final_probability /= len(valid_classifiers)  # 回退到均匀权重

                    final_prediction = np.argmax(final_probability)
                    confidence = np.max(final_probability)

                    # 更新动态权重管理器的性能记录
                    self.dynamic_weight_manager.update_performance(
                        individual_predictions, individual_probabilities
                    )

                    logger.debug(f"动态权重预测: {final_prediction}, 概率: {final_probability}, 置信度: {confidence}")
                else:
                    # 使用简单平均
                    n_classifiers = len(valid_classifiers)
                    final_probability = np.zeros(2)

                    for i in range(n_classifiers):
                        start_idx = i * 2
                        end_idx = start_idx + 2
                        final_probability += meta_features[0, start_idx:end_idx]

                    final_probability /= n_classifiers
                    final_prediction = np.argmax(final_probability)
                    confidence = np.max(final_probability)

                    logger.debug(f"简单平均预测: {final_prediction}, 概率: {final_probability}, 置信度: {confidence}")
            else:
                # 使用正则化元学习器
                final_probability = self.meta_classifier.predict_proba(meta_features)[0]
                final_prediction = np.argmax(final_probability)
                confidence = np.max(final_probability)

                logger.debug(f"元学习器预测: {final_prediction}, 概率: {final_probability}, 置信度: {confidence}")

        except Exception as e:
            logger.warning(f"Stacking预测失败: {e}，使用简单平均作为回退")
            # 回退到简单平均
            n_classifiers = len(valid_classifiers)
            final_probability = np.zeros(2)

            for i in range(n_classifiers):
                start_idx = i * 2
                end_idx = start_idx + 2
                final_probability += meta_features[0, start_idx:end_idx]

            final_probability /= n_classifiers
            final_prediction = np.argmax(final_probability)
            confidence = np.max(final_probability)

        # 应用时序平滑（针对低SNR环境增强稳定性）
        if self.enable_temporal_smoothing:
            smoothing_result = self.temporal_smoother.update(
                final_prediction, final_probability, confidence
            )

            # 使用平滑后的结果
            final_prediction = smoothing_result.smoothed_prediction
            final_probability = smoothing_result.smoothed_probability
            confidence = smoothing_result.confidence

            logger.debug(f"时序平滑: 方法={smoothing_result.smoothing_method}, "
                        f"一致性={smoothing_result.consistency_score:.3f}, "
                        f"缓冲区大小={smoothing_result.buffer_size}")

        # 获取当前触发阈值
        trigger_threshold = self.get_current_thresholds()

        # 分析分类器一致性
        agreement_level = self._analyze_agreement(individual_predictions)

        # 触发决策（简化版：只检查运动想象概率）
        trigger_decision = (
            final_prediction == 1 and
            final_probability[1] >= trigger_threshold
        )

        # 🔧 实时打印各分类器概率值（用于调试和验证）
        if self.enable_realtime_debug:
            print("\n" + "="*80)
            print(f"【实时分类器性能监测】 时间: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}")
            print(f"🔧 触发阈值: {trigger_threshold:.3f} | 最终预测: {'运动想象' if final_prediction == 1 else '休息'}")
            print(f"🔧 最终概率: 休息={final_probability[0]:.3f}, 运动想象={final_probability[1]:.3f}")

            # 🔧 阈值来源验证
            if self.custom_trigger_threshold is not None:
                print(f"🔧 阈值来源: 自定义阈值 {self.custom_trigger_threshold:.3f}")
            else:
                difficulty = self.difficulty_levels[self.current_difficulty]
                print(f"🔧 阈值来源: 难度等级{difficulty.level}默认值 {difficulty.trigger_threshold:.3f}")

            print("-" * 80)
            
            # 打印各个分类器的详细概率
            for name in valid_classifiers:
                pred = individual_predictions[name]
                prob = individual_probabilities[name]
                pred_label = "运动想象" if pred == 1 else "休息"
                print(f"{name:20s} | 预测: {pred_label:4s} | 概率: 休息={prob[0]:.3f}, 运动想象={prob[1]:.3f}")
            
            print("-" * 80)
            print(f"一致性: {agreement_level} | 置信度: {confidence:.3f}")

            # 🔧 详细的触发决策逻辑显示
            print(f"🔧 触发决策分析:")
            print(f"   - 预测结果: {final_prediction} ({'运动想象' if final_prediction == 1 else '休息'})")
            print(f"   - 运动想象概率: {final_probability[1]:.3f}")
            print(f"   - 触发阈值: {trigger_threshold:.3f}")
            print(f"   - 概率 >= 阈值: {final_probability[1]:.3f} >= {trigger_threshold:.3f} = {final_probability[1] >= trigger_threshold}")
            print(f"   - 预测为运动想象: {final_prediction == 1}")
            print(f"   - 最终触发决策: {trigger_decision}")

            if trigger_decision:
                print(f"🔥 ✅ 触发条件满足! 运动想象概率 {final_probability[1]:.3f} >= 阈值 {trigger_threshold:.3f}")
            else:
                if final_prediction == 1:
                    print(f"⚠️ ❌ 运动想象预测但概率不足: {final_probability[1]:.3f} < {trigger_threshold:.3f}")
                else:
                    print(f"📊 ❌ 休息状态预测: {final_probability[0]:.3f}")
            print("="*80)

        # 为Stacking方法创建权重信息（显示用）
        stacking_weights = {name: 1.0/len(valid_classifiers) for name in valid_classifiers}

        return ClassificationResult(
            final_prediction=final_prediction,
            final_probability=final_probability,
            confidence=confidence,
            individual_predictions=individual_predictions,
            individual_probabilities=individual_probabilities,
            voting_weights=stacking_weights,
            trigger_decision=trigger_decision,
            trigger_threshold=trigger_threshold,
            difficulty_level=self.current_difficulty,
            agreement_level=agreement_level
        )

    # 传统投票方法已删除 - 系统仅支持高性能Stacking分类
    
    def _analyze_agreement(self, individual_predictions: Dict[str, int]) -> str:
        """分析分类器一致性"""
        predictions = list(individual_predictions.values())

        if len(set(predictions)) == 1:
            return "完全一致"
        elif predictions.count(predictions[0]) >= len(predictions) * 0.75:
            return "高度一致"
        elif predictions.count(predictions[0]) >= len(predictions) * 0.5:
            return "多数一致"
        else:
            return "分歧较大"

    def display_classification_details(self, result: ClassificationResult) -> str:
        """显示分类详细信息（简化版：只显示各分类器概率）"""
        lines = []

        # 检测是否使用Stacking
        is_stacking = (self.weight_method == "stacking" and self.meta_classifier is not None)
        method_name = "Stacking" if is_stacking else "投票"

        lines.append(f"=== {method_name}分类器概率详情 ===")

        # 各分类器结果
        classifier_names = {
            'fbcsp_svm': 'FBCSP+SVM',
            'tef_rf': 'TEF+RF',
            'riemannian_meanfield': 'Riemannian+MeanField',
            'tangent_space_lr': 'TangentSpace+LR'
        }

        for name, pred in result.individual_predictions.items():
            display_name = classifier_names.get(name, name)
            proba = result.individual_probabilities[name]

            rest_prob = proba[0] * 100
            mi_prob = proba[1] * 100

            lines.append(f"{display_name:18} | 休息: {rest_prob:5.1f}% | 运动想象: {mi_prob:5.1f}%")

        lines.append("-" * 60)

        # 最终结果
        final_rest = result.final_probability[0] * 100
        final_mi = result.final_probability[1] * 100
        final_pred_text = "运动想象" if result.final_prediction == 1 else "休息"

        lines.append(f"{'最终结果':18} | 休息: {final_rest:5.1f}% | 运动想象: {final_mi:5.1f}%")
        lines.append(f"预测: {final_pred_text} | 置信度: {result.confidence:.3f} | 触发: {'是' if result.trigger_decision else '否'}")

        return "\n".join(lines)

    def display_simple_result(self, result: ClassificationResult) -> str:
        """显示简化的分类结果（用于常规监控）"""
        # 检测是否使用Stacking
        is_stacking = (self.weight_method == "stacking" and self.meta_classifier is not None)
        method = "Stacking" if is_stacking else "投票"

        # 基本结果
        final_rest = result.final_probability[0] * 100
        final_mi = result.final_probability[1] * 100
        pred_text = "运动想象" if result.final_prediction == 1 else "休息"
        trigger_text = "触发" if result.trigger_decision else "不触发"

        # 简化输出
        return (f"[{method}] 预测: {pred_text} | "
                f"概率: 休息{final_rest:.1f}% 运动想象{final_mi:.1f}% | "
                f"置信度: {result.confidence:.3f} | "
                f"决策: {trigger_text}")

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态信息"""
        difficulty = self.difficulty_levels[self.current_difficulty]
        trigger_threshold = self.get_current_thresholds()

        return {
            'is_trained': self.is_trained,
            'n_classifiers': len(self.classifiers),
            'classifier_names': list(self.classifiers.keys()),
            'voting_weights': self.voting_weights.copy(),
            'weight_method': self.weight_method,
            'difficulty_level': {
                'level': difficulty.level,
                'name': difficulty.name,
                'description': difficulty.description
            },
            'thresholds': {
                'trigger': trigger_threshold,
                'is_custom': (self.custom_trigger_threshold is not None)
            },
            'performance_summary': {
                name: {
                    'accuracy': perf.accuracy,
                    'cv_mean': perf.cv_mean,
                    'cv_std': perf.cv_std
                }
                for name, perf in self.performance.items()
            }
        }

    def save_configuration(self, config_path: str):
        """保存配置到文件"""
        config = {
            'difficulty_level': self.current_difficulty,
            'custom_trigger_threshold': self.custom_trigger_threshold,
            'weight_method': self.weight_method,
            'use_simple_averaging': self.use_simple_averaging,
            'performance_threshold': self.performance_threshold,
            'custom_weights': self.custom_weights,
            'voting_weights': self.voting_weights,
            'performance': {
                name: {
                    'accuracy': perf.accuracy,
                    'cv_mean': perf.cv_mean,
                    'cv_std': perf.cv_std,
                    'training_time': perf.training_time,
                    'n_samples': perf.n_samples
                }
                for name, perf in self.performance.items()
            }
        }

        try:
            config_file = Path(config_path)

            # 确保目录存在
            from utils.path_manager import path_manager
            path_manager.ensure_directory_exists(config_file.parent)

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            # 验证文件是否真的被写入
            if config_file.exists():
                logger.info(f"配置已保存到: {config_path}")
            else:
                logger.error(f"配置文件保存失败，文件不存在: {config_path}")

        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")

    def load_configuration(self, config_path: str):
        """从文件加载配置"""
        config_file = Path(config_path)

        if not config_file.exists():
            logger.warning(f"配置文件不存在: {config_path}")
            return

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 加载设置
            if 'difficulty_level' in config:
                self.set_difficulty_level(config['difficulty_level'])

            if 'custom_trigger_threshold' in config:
                self.set_custom_thresholds(
                    config.get('custom_trigger_threshold')
                )

            if 'weight_method' in config:
                self.set_weight_method(
                    config['weight_method'],
                    config.get('custom_weights')
                )

            # 加载新的配置参数
            if 'use_simple_averaging' in config:
                self.use_simple_averaging = config['use_simple_averaging']
                logger.info(f"设置简单平均模式: {self.use_simple_averaging}")

            if 'performance_threshold' in config:
                self.performance_threshold = config['performance_threshold']
                logger.info(f"设置性能阈值: {self.performance_threshold}")

            logger.info(f"配置已从文件加载: {config_path}")

        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")


class WeightedVotingManager:
    """加权投票管理器 - 与现有系统集成的接口"""

    def __init__(self):
        self.voting_system = WeightedVotingClassifierSystem()
        self._is_fitted = False

        # 使用路径管理器获取配置文件路径
        from utils.path_manager import get_config_file_in_dir
        self.config_path = str(get_config_file_in_dir("weighted_voting_config.json"))

        # 加载配置（确保使用优化后的配置）
        self.voting_system.load_configuration(self.config_path)
        # print("✅ WeightedVotingManager使用优化配置: weighted_voting_config.json")

    def fit(self, X: np.ndarray, y: np.ndarray, classifiers_data: Dict[str, Any]):
        """训练加权投票系统（支持Stacking）"""
        try:
            # 添加所有分类器到投票系统
            for name, data in classifiers_data.items():
                classifier = data['classifier']
                feature_extractor = data['feature_extractor']
                performance = data['performance']

                self.voting_system.add_classifier(name, classifier, feature_extractor, performance)

            # 训练Stacking集成模型
            if self.voting_system.use_stacking:
                logger.info("开始训练Stacking集成模型...")
                stacking_success = self.voting_system.train_stacking_ensemble(X, y)

                if stacking_success:
                    logger.info("Stacking训练成功")
                else:
                    logger.warning("Stacking训练失败，回退到传统投票方法")
                    self.voting_system.use_stacking = False
                    self.voting_system.weight_method = "performance"
                    self.voting_system._update_voting_weights()
            else:
                # 使用传统投票方法
                self.voting_system._update_voting_weights()

            self._is_fitted = True

            # 保存配置
            self.voting_system.save_configuration(self.config_path)

            logger.info("加权投票系统训练完成")

        except Exception as e:
            logger.error(f"加权投票系统训练失败: {e}")
            raise

    def predict(self, X: np.ndarray) -> ClassificationResult:
        """预测"""
        if not self._is_fitted:
            raise ValueError("加权投票系统未训练")

        return self.voting_system.predict(X)

    def predict_with_details(self, X: np.ndarray, simple_output: bool = False) -> Dict[str, Any]:
        """详细预测（兼容现有接口）"""
        result = self.predict(X)

        # 选择输出格式
        if simple_output:
            details_text = self.voting_system.display_simple_result(result)
        else:
            details_text = self.voting_system.display_classification_details(result)

        return {
            'final_prediction': result.final_prediction,
            'final_probability': result.final_probability,
            'confidence': result.confidence,
            'individual_predictions': result.individual_predictions,
            'individual_probabilities': result.individual_probabilities,
            'voting_weights': result.voting_weights,
            'trigger_decision': result.trigger_decision,
            'difficulty_level': result.difficulty_level,
            'agreement_level': result.agreement_level,
            'classification_details': details_text,
            'simple_result': self.voting_system.display_simple_result(result)  # 总是提供简化版本
        }

    def set_difficulty_level(self, level: int):
        """设置难度等级"""
        self.voting_system.set_difficulty_level(level)
        self.voting_system.save_configuration(self.config_path)

    def set_custom_thresholds(self, trigger_threshold: float = None):
        """设置自定义触发阈值"""
        self.voting_system.set_custom_thresholds(trigger_threshold)
        self.voting_system.save_configuration(self.config_path)

    def set_realtime_debug(self, enabled: bool = True):
        """设置实时调试输出开关（便捷方法）
        
        Args:
            enabled: True启用实时调试输出，False关闭
        """
        self.voting_system.set_realtime_debug(enabled)

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return self.voting_system.get_system_status()
