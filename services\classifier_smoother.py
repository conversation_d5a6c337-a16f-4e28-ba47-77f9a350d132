# -*- coding: utf-8 -*-
"""
分类器输出平滑器
Classifier Output Smoother

基于EEG BCI研究的标准做法，使用指数移动平均(EMA)平滑分类器输出
减少FBCSP等算法的极端化输出，提高系统稳定性
"""

import numpy as np
from typing import Dict, Optional, Union
import logging

logger = logging.getLogger(__name__)


class ExponentialMovingAverageSmoother:
    """
    指数移动平均平滑器
    
    基于EEG BCI研究的标准做法，用于平滑分类器概率输出
    特别适用于FBCSP等容易产生极端输出的算法
    """
    
    def __init__(self, alpha: float = 0.3, min_alpha: float = 0.1, max_alpha: float = 0.7):
        """
        初始化EMA平滑器
        
        Args:
            alpha: 平滑系数 (0-1)，越小越平滑
            min_alpha: 最小平滑系数
            max_alpha: 最大平滑系数
        """
        self.alpha = np.clip(alpha, min_alpha, max_alpha)
        self.min_alpha = min_alpha
        self.max_alpha = max_alpha
        self.previous_prob = None
        self.is_initialized = False
        
        logger.info(f"EMA平滑器初始化，alpha={self.alpha}")
    
    def smooth(self, current_prob: np.ndarray) -> np.ndarray:
        """
        平滑当前概率输出
        
        Args:
            current_prob: 当前概率 [n_classes]
            
        Returns:
            平滑后的概率 [n_classes]
        """
        if not self.is_initialized:
            # 第一次调用，直接返回当前概率
            self.previous_prob = current_prob.copy()
            self.is_initialized = True
            return current_prob
        
        # EMA公式: smoothed = alpha * current + (1 - alpha) * previous
        smoothed_prob = self.alpha * current_prob + (1 - self.alpha) * self.previous_prob
        
        # 确保概率和为1
        smoothed_prob = smoothed_prob / np.sum(smoothed_prob)
        
        # 更新历史
        self.previous_prob = smoothed_prob.copy()
        
        return smoothed_prob
    
    def reset(self):
        """重置平滑器状态"""
        self.previous_prob = None
        self.is_initialized = False
        logger.debug("EMA平滑器状态已重置")
    
    def set_alpha(self, alpha: float):
        """动态调整平滑系数"""
        self.alpha = np.clip(alpha, self.min_alpha, self.max_alpha)
        logger.debug(f"EMA平滑系数调整为: {self.alpha}")


class AdaptiveClassifierSmoother:
    """
    自适应分类器平滑器
    
    为不同的分类器使用不同的平滑策略
    """
    
    def __init__(self, classifier_configs: Optional[Dict[str, Dict]] = None):
        """
        初始化自适应平滑器
        
        Args:
            classifier_configs: 各分类器的平滑配置
        """
        # 默认配置
        default_configs = {
            'fbcsp_svm': {'enabled': True, 'alpha': 0.3},  # FBCSP需要较强平滑
            'riemannian_meanfield': {'enabled': False, 'alpha': 0.2},  # Riemannian相对稳定
            'tangent_space_lr': {'enabled': True, 'alpha': 0.2},  # 轻微平滑
            'plv_svm': {'enabled': False, 'alpha': 0.1},  # PLV输出已经很平滑
            'tef_rf': {'enabled': True, 'alpha': 0.25}  # TEF适度平滑
        }
        
        # 合并用户配置
        if classifier_configs:
            default_configs.update(classifier_configs)
        
        self.configs = default_configs
        self.smoothers = {}
        
        # 为启用的分类器创建平滑器
        for name, config in self.configs.items():
            if config.get('enabled', False):
                self.smoothers[name] = ExponentialMovingAverageSmoother(
                    alpha=config.get('alpha', 0.3)
                )
                logger.info(f"为{name}创建平滑器，alpha={config.get('alpha', 0.3)}")
    
    def smooth_classifier_output(self, classifier_name: str, 
                                probabilities: np.ndarray) -> np.ndarray:
        """
        平滑指定分类器的输出
        
        Args:
            classifier_name: 分类器名称
            probabilities: 原始概率输出
            
        Returns:
            平滑后的概率输出
        """
        if classifier_name in self.smoothers:
            smoothed = self.smoothers[classifier_name].smooth(probabilities)
            
            # 记录平滑效果
            max_change = np.max(np.abs(smoothed - probabilities))
            if max_change > 0.05:  # 只记录显著变化
                logger.debug(f"{classifier_name}平滑: 最大变化{max_change:.3f}")
            
            return smoothed
        else:
            # 未配置平滑的分类器直接返回原始输出
            return probabilities
    
    def smooth_all_outputs(self, classifier_outputs: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """
        平滑所有分类器输出
        
        Args:
            classifier_outputs: {classifier_name: probabilities}
            
        Returns:
            平滑后的输出字典
        """
        smoothed_outputs = {}
        
        for name, probabilities in classifier_outputs.items():
            smoothed_outputs[name] = self.smooth_classifier_output(name, probabilities)
        
        return smoothed_outputs
    
    def reset_all(self):
        """重置所有平滑器"""
        for smoother in self.smoothers.values():
            smoother.reset()
        logger.info("所有分类器平滑器已重置")
    
    def get_smoother_status(self) -> Dict[str, Dict]:
        """获取平滑器状态"""
        status = {}
        for name, config in self.configs.items():
            status[name] = {
                'enabled': config.get('enabled', False),
                'alpha': config.get('alpha', 0.0),
                'has_smoother': name in self.smoothers,
                'initialized': (self.smoothers[name].is_initialized 
                              if name in self.smoothers else False)
            }
        return status


def create_classifier_smoother(config: Optional[Dict] = None) -> AdaptiveClassifierSmoother:
    """
    创建分类器平滑器的工厂函数
    
    Args:
        config: 平滑器配置
        
    Returns:
        配置好的平滑器实例
    """
    return AdaptiveClassifierSmoother(config)


# 使用示例和测试
if __name__ == "__main__":
    # 测试EMA平滑器
    smoother = ExponentialMovingAverageSmoother(alpha=0.3)
    
    # 模拟FBCSP的极端输出
    extreme_outputs = [
        np.array([0.05, 0.95]),  # 极端运动想象
        np.array([0.03, 0.97]),  # 更极端
        np.array([0.85, 0.15]),  # 极端休息
        np.array([0.92, 0.08])   # 更极端休息
    ]
    
    print("FBCSP平滑测试:")
    for i, output in enumerate(extreme_outputs):
        smoothed = smoother.smooth(output)
        print(f"步骤{i+1}: {output} -> {smoothed}")
    
    print("\n自适应平滑器测试:")
    adaptive_smoother = create_classifier_smoother()
    status = adaptive_smoother.get_smoother_status()
    for name, info in status.items():
        print(f"{name}: enabled={info['enabled']}, alpha={info['alpha']}")
