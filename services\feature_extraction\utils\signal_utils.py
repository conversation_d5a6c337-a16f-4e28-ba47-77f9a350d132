"""
信号处理工具函数

提供EEG信号预处理和分析的通用函数。
"""

import numpy as np
import scipy.signal as signal
import scipy.stats as stats
from scipy.fft import fft, fftfreq
from typing import Tuple, List, Optional, Union
import logging

logger = logging.getLogger(__name__)

def butter_bandpass_filter(data: np.ndarray, 
                          lowcut: float, 
                          highcut: float, 
                          fs: float, 
                          order: int = 4) -> np.ndarray:
    """
    Butterworth带通滤波器
    
    Args:
        data: 输入信号 [n_channels, n_samples] 或 [n_samples]
        lowcut: 低频截止频率 (Hz)
        highcut: 高频截止频率 (Hz)
        fs: 采样频率 (Hz)
        order: 滤波器阶数
        
    Returns:
        滤波后的信号
    """
    try:
        nyquist = 0.5 * fs
        low = lowcut / nyquist
        high = highcut / nyquist
        
        if low <= 0 or high >= 1:
            raise ValueError(f"频率范围无效: {lowcut}-{highcut} Hz (Nyquist: {nyquist} Hz)")
        
        b, a = signal.butter(order, [low, high], btype='band')
        
        if data.ndim == 1:
            return signal.filtfilt(b, a, data)
        else:
            # 对每个通道分别滤波
            filtered_data = np.zeros_like(data)
            for ch in range(data.shape[0]):
                filtered_data[ch] = signal.filtfilt(b, a, data[ch])
            return filtered_data
            
    except Exception as e:
        logger.error(f"带通滤波失败: {e}")
        raise

def apply_car_reference(data: np.ndarray) -> np.ndarray:
    """
    应用共同平均参考 (Common Average Reference, CAR)
    
    Args:
        data: 输入信号 [n_channels, n_samples]
        
    Returns:
        CAR处理后的信号
    """
    try:
        if data.ndim != 2:
            raise ValueError("输入数据应为2维 [n_channels, n_samples]")
        
        # 计算所有通道的平均值
        car_signal = np.mean(data, axis=0, keepdims=True)
        
        # 从每个通道减去平均值
        return data - car_signal
        
    except Exception as e:
        logger.error(f"CAR参考失败: {e}")
        raise

def compute_psd(data: np.ndarray, 
                fs: float, 
                nperseg: Optional[int] = None,
                method: str = 'welch') -> Tuple[np.ndarray, np.ndarray]:
    """
    计算功率谱密度
    
    Args:
        data: 输入信号 [n_channels, n_samples] 或 [n_samples]
        fs: 采样频率 (Hz)
        nperseg: 每段长度，默认为fs
        method: 计算方法 ('welch', 'periodogram')
        
    Returns:
        (frequencies, psd): 频率数组和功率谱密度
    """
    try:
        if nperseg is None:
            nperseg = int(fs)
        
        if method == 'welch':
            if data.ndim == 1:
                freqs, psd = signal.welch(data, fs, nperseg=nperseg)
            else:
                freqs, psd = signal.welch(data, fs, nperseg=nperseg, axis=1)
        elif method == 'periodogram':
            if data.ndim == 1:
                freqs, psd = signal.periodogram(data, fs)
            else:
                freqs, psd = signal.periodogram(data, fs, axis=1)
        else:
            raise ValueError(f"不支持的方法: {method}")
        
        return freqs, psd
        
    except Exception as e:
        logger.error(f"PSD计算失败: {e}")
        raise

def extract_frequency_bands(data: np.ndarray, 
                           fs: float,
                           bands: List[Tuple[float, float]]) -> List[np.ndarray]:
    """
    提取指定频段的信号
    
    Args:
        data: 输入信号 [n_channels, n_samples]
        fs: 采样频率 (Hz)
        bands: 频段列表 [(low1, high1), (low2, high2), ...]
        
    Returns:
        各频段滤波后的信号列表
    """
    try:
        filtered_signals = []
        
        for low, high in bands:
            filtered_signal = butter_bandpass_filter(data, low, high, fs)
            filtered_signals.append(filtered_signal)
        
        return filtered_signals
        
    except Exception as e:
        logger.error(f"频段提取失败: {e}")
        raise

def normalize_signal(data: np.ndarray, 
                     method: str = 'zscore',
                     axis: Optional[int] = None) -> np.ndarray:
    """
    信号标准化
    
    Args:
        data: 输入信号
        method: 标准化方法 ('zscore', 'minmax', 'robust')
        axis: 标准化轴，None表示全局标准化
        
    Returns:
        标准化后的信号
    """
    try:
        if method == 'zscore':
            return stats.zscore(data, axis=axis)
        elif method == 'minmax':
            data_min = np.min(data, axis=axis, keepdims=True)
            data_max = np.max(data, axis=axis, keepdims=True)
            return (data - data_min) / (data_max - data_min + 1e-8)
        elif method == 'robust':
            median = np.median(data, axis=axis, keepdims=True)
            mad = np.median(np.abs(data - median), axis=axis, keepdims=True)
            return (data - median) / (mad + 1e-8)
        else:
            raise ValueError(f"不支持的标准化方法: {method}")
            
    except Exception as e:
        logger.error(f"信号标准化失败: {e}")
        raise

def remove_artifacts(data: np.ndarray, 
                    threshold: float = 3.0,
                    method: str = 'zscore') -> Tuple[np.ndarray, np.ndarray]:
    """
    移除伪迹
    
    Args:
        data: 输入信号 [n_trials, n_channels, n_samples]
        threshold: 阈值
        method: 检测方法 ('zscore', 'iqr')
        
    Returns:
        (clean_data, artifact_mask): 清洁数据和伪迹掩码
    """
    try:
        if data.ndim != 3:
            raise ValueError("输入数据应为3维 [n_trials, n_channels, n_samples]")
        
        n_trials = data.shape[0]
        artifact_mask = np.zeros(n_trials, dtype=bool)
        
        for trial in range(n_trials):
            trial_data = data[trial]
            
            if method == 'zscore':
                # 计算每个通道的Z分数
                z_scores = np.abs(stats.zscore(trial_data, axis=1))
                max_z = np.max(z_scores)
                if max_z > threshold:
                    artifact_mask[trial] = True
                    
            elif method == 'iqr':
                # 使用四分位距检测异常值
                for ch in range(trial_data.shape[0]):
                    q75, q25 = np.percentile(trial_data[ch], [75, 25])
                    iqr = q75 - q25
                    lower_bound = q25 - threshold * iqr
                    upper_bound = q75 + threshold * iqr
                    
                    if np.any(trial_data[ch] < lower_bound) or np.any(trial_data[ch] > upper_bound):
                        artifact_mask[trial] = True
                        break
            else:
                raise ValueError(f"不支持的检测方法: {method}")
        
        # 返回清洁数据
        clean_data = data[~artifact_mask]
        
        logger.info(f"检测到{np.sum(artifact_mask)}个伪迹试次，"
                   f"保留{len(clean_data)}个清洁试次")
        
        return clean_data, artifact_mask
        
    except Exception as e:
        logger.error(f"伪迹移除失败: {e}")
        raise

def compute_envelope(data: np.ndarray, method: str = 'hilbert') -> np.ndarray:
    """
    计算信号包络
    
    Args:
        data: 输入信号
        method: 计算方法 ('hilbert', 'rms')
        
    Returns:
        信号包络
    """
    try:
        if method == 'hilbert':
            analytic_signal = signal.hilbert(data, axis=-1)
            return np.abs(analytic_signal)
        elif method == 'rms':
            # 滑动窗口RMS
            window_size = int(0.1 * data.shape[-1])  # 10%窗口
            envelope = np.zeros_like(data)
            
            for i in range(data.shape[-1]):
                start = max(0, i - window_size // 2)
                end = min(data.shape[-1], i + window_size // 2)
                envelope[..., i] = np.sqrt(np.mean(data[..., start:end] ** 2, axis=-1))
            
            return envelope
        else:
            raise ValueError(f"不支持的包络计算方法: {method}")
            
    except Exception as e:
        logger.error(f"包络计算失败: {e}")
        raise

def compute_coherence(x: np.ndarray, 
                     y: np.ndarray, 
                     fs: float,
                     nperseg: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
    """
    计算两个信号间的相干性
    
    Args:
        x, y: 输入信号
        fs: 采样频率
        nperseg: 每段长度
        
    Returns:
        (frequencies, coherence): 频率和相干性
    """
    try:
        if nperseg is None:
            nperseg = int(fs)
        
        freqs, coh = signal.coherence(x, y, fs, nperseg=nperseg)
        return freqs, coh
        
    except Exception as e:
        logger.error(f"相干性计算失败: {e}")
        raise

def apply_notch_filter(data: np.ndarray, 
                      notch_freq: float, 
                      fs: float,
                      quality_factor: float = 30.0) -> np.ndarray:
    """
    应用陷波滤波器（去除工频干扰）
    
    Args:
        data: 输入信号
        notch_freq: 陷波频率 (Hz)
        fs: 采样频率 (Hz)
        quality_factor: 品质因子
        
    Returns:
        滤波后的信号
    """
    try:
        b, a = signal.iirnotch(notch_freq, quality_factor, fs)
        
        if data.ndim == 1:
            return signal.filtfilt(b, a, data)
        else:
            filtered_data = np.zeros_like(data)
            for ch in range(data.shape[0]):
                filtered_data[ch] = signal.filtfilt(b, a, data[ch])
            return filtered_data
            
    except Exception as e:
        logger.error(f"陷波滤波失败: {e}")
        raise
