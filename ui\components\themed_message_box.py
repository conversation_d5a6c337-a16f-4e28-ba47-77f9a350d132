# -*- coding: utf-8 -*-
"""
主题化消息框组件
Themed Message Box Component

提供支持主题切换的消息框
"""

from PySide6.QtWidgets import QMessageBox, QWidget, QFrame, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QDialog
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont



def _create_themed_message_box(parent):
    """创建主题化消息框 - 使用圆角容器"""
    msg_box = QMessageBox(parent)

    # 设置无边框窗口 - 与其他对话框保持一致
    msg_box.setWindowFlags(
        Qt.WindowType.FramelessWindowHint |
        Qt.WindowType.Dialog |
        Qt.WindowType.WindowStaysOnTopHint
    )
    msg_box.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
    msg_box.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)

    # 应用全局样式
    if parent and hasattr(parent, 'styleSheet') and parent.styleSheet():
        msg_box.setStyleSheet(parent.styleSheet())

    # 设置字体
    font = QFont("Microsoft YaHei", 12)
    msg_box.setFont(font)

    return msg_box

def _get_global_stylesheet(parent):
    """获取全局样式表"""
    # 方法1：从父窗口获取
    if parent:
        parent_widget = parent
        level = 0
        while parent_widget and level < 10:
            if hasattr(parent_widget, 'theme_manager'):
                return parent_widget.theme_manager.get_current_stylesheet()
            parent_widget = parent_widget.parent()
            level += 1

    # 方法2：从QApplication获取
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'theme_manager'):
                    return widget.theme_manager.get_current_stylesheet()
    except:
        pass

    # 方法3：返回应用程序的样式表
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            return app.styleSheet()
    except:
        pass

    return ""


class ThemedMessageBox(QDialog):
    """自定义圆角消息框"""

    def __init__(self, parent=None, title="", text="", icon_type="information", buttons=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(420, 220)

        # 设置无边框窗口
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowStaysOnTopHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)

        # 应用全局样式
        if parent and hasattr(parent, 'styleSheet') and parent.styleSheet():
            self.setStyleSheet(parent.styleSheet())

        self.result_value = QMessageBox.StandardButton.NoButton
        self._init_ui(title, text, icon_type, buttons)

    def _init_ui(self, title, text, icon_type, buttons):
        """初始化UI"""
        # 主容器
        main_container = QFrame()
        main_container.setObjectName("discharge_dialog_container")

        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)

        # 标题和图标区域
        title_layout = QHBoxLayout()
        title_layout.setSpacing(12)
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 图标
        icon_label = QLabel()
        icon_label.setObjectName(f"dialog_icon_{icon_type}")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setFixedSize(36, 36)

        # 根据类型设置图标
        icon_text = self._get_icon_text(icon_type)
        icon_label.setText(icon_text)
        icon_label.setFont(QFont("Microsoft YaHei", 22, QFont.Weight.Bold))

        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title_label.setObjectName("dialog_title")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)

        layout.addLayout(title_layout)

        # 消息内容
        message_label = QLabel(text)
        message_label.setFont(QFont("Microsoft YaHei", 12))
        message_label.setObjectName("dialog_info")
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()  # 左侧弹性空间

        if buttons is None:
            buttons = QMessageBox.StandardButton.Ok

        # 设置按钮固定尺寸
        button_width = 80
        button_height = 32

        if buttons & QMessageBox.StandardButton.Ok:
            ok_btn = QPushButton("确定")
            ok_btn.setObjectName("btn_primary")
            ok_btn.setFont(QFont("Microsoft YaHei", 12))
            ok_btn.setFixedSize(button_width, button_height)
            ok_btn.clicked.connect(lambda: self._set_result_and_close(QMessageBox.StandardButton.Ok))
            button_layout.addWidget(ok_btn)

        if buttons & QMessageBox.StandardButton.Yes:
            yes_btn = QPushButton("是")
            yes_btn.setObjectName("btn_primary")
            yes_btn.setFont(QFont("Microsoft YaHei", 12))
            yes_btn.setFixedSize(button_width, button_height)
            yes_btn.clicked.connect(lambda: self._set_result_and_close(QMessageBox.StandardButton.Yes))
            button_layout.addWidget(yes_btn)

        # 在多个按钮之间添加间距
        button_count = 0
        if buttons & QMessageBox.StandardButton.Ok:
            button_count += 1
        if buttons & QMessageBox.StandardButton.Yes:
            button_count += 1
        if buttons & QMessageBox.StandardButton.No:
            button_count += 1
        if buttons & QMessageBox.StandardButton.Cancel:
            button_count += 1

        # 当有多个按钮时，在它们之间添加间距
        if button_count > 1 and (buttons & QMessageBox.StandardButton.Yes):
            if (buttons & QMessageBox.StandardButton.No) or (buttons & QMessageBox.StandardButton.Cancel):
                button_layout.addSpacing(16)

        if buttons & QMessageBox.StandardButton.No:
            no_btn = QPushButton("否")
            no_btn.setObjectName("btn_secondary")
            no_btn.setFont(QFont("Microsoft YaHei", 12))
            no_btn.setFixedSize(button_width, button_height)
            no_btn.clicked.connect(lambda: self._set_result_and_close(QMessageBox.StandardButton.No))
            button_layout.addWidget(no_btn)

        # 在"否"和"取消"按钮之间添加间距
        if (buttons & QMessageBox.StandardButton.No) and (buttons & QMessageBox.StandardButton.Cancel):
            button_layout.addSpacing(16)

        if buttons & QMessageBox.StandardButton.Cancel:
            cancel_btn = QPushButton("取消")
            cancel_btn.setObjectName("btn_secondary")
            cancel_btn.setFont(QFont("Microsoft YaHei", 12))
            cancel_btn.setFixedSize(button_width, button_height)
            cancel_btn.clicked.connect(lambda: self._set_result_and_close(QMessageBox.StandardButton.Cancel))
            button_layout.addWidget(cancel_btn)

        button_layout.addStretch()  # 右侧弹性空间

        layout.addLayout(button_layout)

        # 设置主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_container)

    def _set_result_and_close(self, result):
        """设置结果并关闭对话框"""
        self.result_value = result
        if result in [QMessageBox.StandardButton.Ok, QMessageBox.StandardButton.Yes]:
            self.accept()
        else:
            self.reject()

    def _get_icon_text(self, icon_type):
        """根据图标类型获取图标文本"""
        icon_map = {
            "information": "ℹ",
            "warning": "⚠",
            "critical": "✕",
            "question": "?"
        }
        return icon_map.get(icon_type, "ℹ")

    def paintEvent(self, event):
        """绘制圆角背景 - 让全局样式处理背景"""
        super().paintEvent(event)


# 便捷函数
def show_information(parent, title, text):
    """显示信息消息框 - 圆角样式"""
    msg_box = ThemedMessageBox(parent, title, text, "information", QMessageBox.StandardButton.Ok)
    msg_box.exec()
    return msg_box.result_value

def show_warning(parent, title, text):
    """显示警告消息框 - 圆角样式"""
    msg_box = ThemedMessageBox(parent, title, text, "warning", QMessageBox.StandardButton.Ok)
    msg_box.exec()
    return msg_box.result_value

def show_critical(parent, title, text):
    """显示错误消息框 - 圆角样式"""
    msg_box = ThemedMessageBox(parent, title, text, "critical", QMessageBox.StandardButton.Ok)
    msg_box.exec()
    return msg_box.result_value

def show_question(parent, title, text, buttons=QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No):
    """显示询问消息框 - 圆角样式"""
    msg_box = ThemedMessageBox(parent, title, text, "question", buttons)
    msg_box.exec()
    return msg_box.result_value

# 兼容性别名
information = show_information
warning = show_warning
critical = show_critical
question = show_question
