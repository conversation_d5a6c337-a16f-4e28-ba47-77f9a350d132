"""
脑电信号预处理配置管理

统一管理三级预处理流程的参数配置：
- 传统滤波：陷波、带通、标准化
- RLS自适应滤波：快速收敛，实时自适应去噪
- Kalman滤波：状态估计，精细去噪

支持灵活的算法组合和参数调整，满足32ms实时处理要求
"""

import numpy as np
from typing import List, Tuple


class PreprocessingConfig:
    """脑电信号预处理配置类"""
    
    def __init__(self):
        # ==================== 处理步骤开关 ====================
        # 注释掉对应行可禁用相应的处理步骤

        self.enable_traditional_filter = True    # 传统滤波（陷波+带通，无标准化）
        self.enable_rls_filter = True           # RLS自适应滤波 （基于验证结果）
        self.enable_kalman_filter = False        # Kalman自适应滤波 - 禁用（存在长期漂移问题）
        
        # ==================== 基础参数 ====================
        self.sampling_rate = 125                 # 采样率 125Hz
        self.n_channels = 8                     # 通道数
        self.channel_names = ['PZ', 'P3', 'P4', 'C3', 'CZ', 'C4', 'F3', 'F4']
        
        # ==================== 传统滤波参数 ====================
        # 陷波滤波器参数（针对低SNR优化）
        self.notch_freq = 50                    # 陷波频率（工频干扰）
        self.notch_q = 10                       # 陷波滤波器Q值（进一步降低Q值，扩大陷波范围）

        # 带通滤波器参数（扩展频段，降低阶数，减少相位失真）
        self.bandpass_low = 4                   # 带通低频截止（保留更多低频信息）
        self.bandpass_high = 40                 # 带通高频截止（保留更多高频信息）
        self.bandpass_order = 4                 # 滤波器阶数（降低阶数减少相位失真）
        
        # 通道标准化参数
        self.enable_standardization = True     # 标准化
        self.standardization_epsilon = 1e-8     # 防止除零的小常数
        
        # ==================== RLS自适应滤波器参数 ====================
        # 用于低SNR脑电信号的实时自适应滤波（进一步优化以避免长期漂移）
        self.rls_filter_length = 12             # RLS滤波器长度（减少以提高稳定性）
        self.rls_forgetting_factor = 0.999      # 遗忘因子（进一步提高以避免长期漂移）
        self.rls_regularization = 1e-3          # 正则化参数（增加以提高数值稳定性）
        self.rls_initial_covariance = 10        # 初始协方差矩阵对角元素（进一步降低）
        
        # ==================== Kalman滤波器参数 ====================
        # 用于低SNR脑电信号的自适应去噪（保守参数避免长期漂移）
        self.kalman_process_noise_var = 1.0     # 过程噪声方差（大幅增加以允许信号自由变化）
        self.kalman_observation_noise_var = 0.1 # 观测噪声方差（降低以减少过度平滑）
        self.kalman_adaptation_window = 200     # 自适应窗口大小（大幅增加以提高稳定性）
        self.kalman_enable_adaptation = False   # 禁用自适应调整以避免参数漂移
        
        # ==================== 性能监控参数 ====================
        self.enable_performance_monitoring = True   # 启用性能监控
        self.performance_log_interval = 100         # 每100次处理记录一次性能
        
        # ==================== 调试参数 ====================
        self.enable_debug_output = False           # 启用调试输出
        self.save_intermediate_results = False     # 保存中间结果
        
    def get_enabled_steps(self) -> List[str]:
        """获取启用的处理步骤列表"""
        enabled_steps = []

        if self.enable_traditional_filter:
            enabled_steps.append("traditional_filter")
        if self.enable_rls_filter:
            enabled_steps.append("rls_filter")
        if self.enable_kalman_filter:
            enabled_steps.append("kalman_filter")

        return enabled_steps
    
    def validate_config(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证采样率
            assert self.sampling_rate > 0, "采样率必须大于0"
            
            # 验证通道数
            assert self.n_channels > 0, "通道数必须大于0"
            assert len(self.channel_names) == self.n_channels, "通道名称数量与通道数不匹配"
            
            # 验证滤波器参数
            if self.enable_traditional_filter:
                assert 0 < self.bandpass_low < self.bandpass_high < self.sampling_rate/2, \
                    "带通滤波器频率范围无效"
                assert 0 < self.notch_freq < self.sampling_rate/2, "陷波频率无效"
                assert self.bandpass_order > 0, "滤波器阶数必须大于0"
            
            # 验证Kalman滤波器参数
            if self.enable_kalman_filter:
                assert self.kalman_process_noise_var > 0, "Kalman过程噪声方差必须大于0"
                assert self.kalman_observation_noise_var > 0, "Kalman观测噪声方差必须大于0"
                assert self.kalman_adaptation_window > 0, "Kalman自适应窗口必须大于0"

            # 验证RLS滤波器参数
            if self.enable_rls_filter:
                assert self.rls_filter_length > 0, "RLS滤波器长度必须大于0"
                assert 0 < self.rls_forgetting_factor <= 1, "RLS遗忘因子必须在(0,1]范围内"
                assert self.rls_regularization > 0, "RLS正则化参数必须大于0"
                assert self.rls_initial_covariance > 0, "RLS初始协方差必须大于0"
            
            return True
            
        except AssertionError as e:
            print(f"配置验证失败: {e}")
            return False
    
    def get_config_summary(self) -> dict:
        """获取配置摘要信息"""
        return {
            "enabled_steps": self.get_enabled_steps(),
            "sampling_rate": self.sampling_rate,
            "n_channels": self.n_channels,
            "bandpass_range": (self.bandpass_low, self.bandpass_high) if self.enable_traditional_filter else None,
            "kalman_noise_vars": (self.kalman_process_noise_var, self.kalman_observation_noise_var) if self.enable_kalman_filter else None,
            "rls_config": {
                "filter_length": self.rls_filter_length,
                "forgetting_factor": self.rls_forgetting_factor,
                "regularization": self.rls_regularization,
                "initial_covariance": self.rls_initial_covariance
            } if self.enable_rls_filter else None,
            "performance_monitoring": self.enable_performance_monitoring
        }
    
    def copy(self):
        """创建配置的深拷贝"""
        new_config = PreprocessingConfig()
        
        # 复制所有属性
        for attr_name in dir(self):
            if not attr_name.startswith('_') and not callable(getattr(self, attr_name)):
                setattr(new_config, attr_name, getattr(self, attr_name))
        
        return new_config


# 默认配置实例
DEFAULT_CONFIG = PreprocessingConfig()

# 快速配置预设
class QuickConfigs:
    """预定义的快速配置"""
    
    @staticmethod
    def only_traditional_filter():
        """仅启用传统滤波"""
        config = PreprocessingConfig()
        config.enable_traditional_filter = True
        config.enable_rls_filter = False
        config.enable_kalman_filter = False
        return config

    @staticmethod
    def traditional_and_rls():
        """启用传统滤波和RLS滤波"""
        config = PreprocessingConfig()
        config.enable_traditional_filter = True
        config.enable_rls_filter = True
        config.enable_kalman_filter = False
        return config

    @staticmethod
    def traditional_and_kalman():
        """启用传统滤波和Kalman滤波"""
        config = PreprocessingConfig()
        config.enable_traditional_filter = True
        config.enable_rls_filter = False
        config.enable_kalman_filter = True
        return config

    @staticmethod
    def full_pipeline():
        """启用完整的三级预处理（传统+RLS+Kalman）"""
        config = PreprocessingConfig()
        config.enable_traditional_filter = True
        config.enable_rls_filter = True
        config.enable_kalman_filter = True
        return config

    @staticmethod
    def only_rls_filter():
        """仅启用RLS自适应滤波"""
        config = PreprocessingConfig()
        config.enable_traditional_filter = False
        config.enable_rls_filter = True
        config.enable_kalman_filter = False
        return config
    
    @staticmethod
    def debug_mode():
        """调试模式配置"""
        config = PreprocessingConfig()
        config.enable_debug_output = True
        config.save_intermediate_results = True
        config.enable_performance_monitoring = True
        return config
