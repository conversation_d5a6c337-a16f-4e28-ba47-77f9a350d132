"""
Riemannian几何协方差矩阵特征提取器

基于pyRiemann库实现的Riemannian几何特征提取，专门用于运动想象EEG信号分类。
使用协方差矩阵的Riemannian几何特性提取空间相关性特征。
"""

import numpy as np
import logging
from typing import Optional, List, Dict, Any

try:
    from pyriemann.estimation import Covariances
    from pyriemann.tangentspace import TangentSpace
    from pyriemann.utils.mean import mean_covariance
    PYRIEMANN_AVAILABLE = True
except ImportError:
    PYRIEMANN_AVAILABLE = False
    Covariances = None
    TangentSpace = None
    mean_covariance = None

from .base_extractor import BaseFeatureExtractor

logger = logging.getLogger(__name__)

class RiemannianCovarianceExtractor(BaseFeatureExtractor):
    """
    Riemannian几何协方差矩阵特征提取器
    
    基于pyRiemann库实现，将EEG信号的空间协方差矩阵视为对称正定矩阵（SPD），
    在Riemannian流形上进行特征提取。特别适合小样本运动想象分类任务。
    """
    
    def __init__(self,
                 metric: str = 'riemann',
                 estimator: str = 'cov',
                 tangent_space: bool = True,
                 reference_method: str = 'mean',
                 regularization: float = 1e-6,
                 **kwargs):
        """
        初始化Riemannian特征提取器

        Args:
            metric: Riemannian度量 ('riemann', 'logeuclid', 'euclid')
            estimator: 协方差估计器 ('cov', 'oas', 'lwf', 'mcd', 'sch')
            tangent_space: 是否使用切空间映射
            reference_method: 参考点计算方法 ('mean', 'median')
            regularization: 协方差矩阵正则化参数
            **kwargs: 传递给基类的参数
        """
        super().__init__(**kwargs)
        
        if not PYRIEMANN_AVAILABLE:
            raise ImportError("需要安装pyRiemann库: pip install pyriemann")
        
        self.metric = metric
        self.estimator = estimator
        self.tangent_space = tangent_space
        self.reference_method = reference_method
        self.regularization = regularization
        
        # 初始化pyRiemann组件
        self.cov_estimator = Covariances(estimator=estimator)
        
        if tangent_space:
            self.tangent_mapper = TangentSpace(metric=metric)
        
        # 内部状态
        self._reference_matrix = None
        self._is_fitted = False
        
        logger.info(f"Riemannian特征提取器初始化完成")
        logger.info(f"  度量: {metric}, 估计器: {estimator}")
        logger.info(f"  切空间映射: {tangent_space}, 正则化: {regularization}")
    
    def _fit(self, X: np.ndarray, y: np.ndarray) -> 'RiemannianCovarianceExtractor':
        """
        训练Riemannian特征提取器
        
        Args:
            X: 训练数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]
            
        Returns:
            self
        """
        try:
            logger.info(f"开始训练Riemannian特征提取器，数据形状: {X.shape}")
            
            # 添加正则化以提高数值稳定性
            if self.regularization > 0:
                X_reg = X.copy()
                for i in range(X_reg.shape[0]):
                    # 对每个试次添加少量正则化
                    X_reg[i] += np.random.normal(0, self.regularization, X_reg[i].shape)
            else:
                X_reg = X
            
            # 估计协方差矩阵
            logger.debug("正在估计协方差矩阵...")
            cov_matrices = self.cov_estimator.fit_transform(X_reg)
            logger.debug(f"协方差矩阵形状: {cov_matrices.shape}")
            
            if self.tangent_space:
                # 训练切空间映射器
                logger.debug("正在训练切空间映射器...")
                self.tangent_mapper.fit(cov_matrices, y)

                # 保存参考矩阵
                self._reference_matrix = self.tangent_mapper.reference_
                logger.debug(f"参考矩阵形状: {self._reference_matrix.shape}")
            
            self._is_fitted = True
            logger.info("Riemannian特征提取器训练完成")
            
            return self
            
        except Exception as e:
            logger.error(f"Riemannian特征提取器训练失败: {e}")
            raise
    
    def _transform(self, X: np.ndarray) -> np.ndarray:
        """
        提取Riemannian特征
        
        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]
            
        Returns:
            Riemannian特征 [n_trials, n_features]
        """
        try:
            if not self._is_fitted:
                raise ValueError("特征提取器未训练，请先调用fit方法")
            
            logger.debug(f"开始Riemannian特征提取，数据形状: {X.shape}")
            
            # 估计协方差矩阵
            cov_matrices = self.cov_estimator.transform(X)
            logger.debug(f"协方差矩阵形状: {cov_matrices.shape}")
            
            if self.tangent_space:
                # 切空间映射
                features = self.tangent_mapper.transform(cov_matrices)
                logger.debug(f"切空间特征形状: {features.shape}")
            else:
                # 直接向量化上三角矩阵
                features = self._vectorize_covariance_matrices(cov_matrices)
                logger.debug(f"向量化特征形状: {features.shape}")
            
            logger.debug(f"Riemannian特征提取完成，特征维度: {features.shape[1]}")
            return features
            
        except Exception as e:
            logger.error(f"Riemannian特征提取失败: {e}")
            raise
    
    def _vectorize_covariance_matrices(self, cov_matrices: np.ndarray) -> np.ndarray:
        """
        向量化协方差矩阵（提取上三角部分）
        
        Args:
            cov_matrices: 协方差矩阵 [n_trials, n_channels, n_channels]
            
        Returns:
            向量化特征 [n_trials, n_features]
        """
        try:
            n_trials, n_channels, _ = cov_matrices.shape
            
            # 获取上三角索引（包括对角线）
            triu_indices = np.triu_indices(n_channels)
            n_features = len(triu_indices[0])
            
            # 提取上三角部分
            features = np.zeros((n_trials, n_features))
            for i in range(n_trials):
                features[i] = cov_matrices[i][triu_indices]
            
            return features
            
        except Exception as e:
            logger.error(f"协方差矩阵向量化失败: {e}")
            raise
    
    def get_feature_names(self) -> List[str]:
        """
        获取特征名称列表
        
        Returns:
            特征名称列表
        """
        if self.tangent_space:
            # 切空间特征
            n_features = self.n_channels * (self.n_channels + 1) // 2
            return [f'riemannian_tangent_{i}' for i in range(n_features)]
        else:
            # 协方差矩阵上三角特征
            feature_names = []
            for i in range(self.n_channels):
                for j in range(i, self.n_channels):
                    if i == j:
                        feature_names.append(f'riemannian_cov_ch{i}_ch{j}_diag')
                    else:
                        feature_names.append(f'riemannian_cov_ch{i}_ch{j}')
            return feature_names
    
    def get_covariance_matrices(self, X: np.ndarray) -> np.ndarray:
        """
        获取协方差矩阵（用于MDM分类器）
        
        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]
            
        Returns:
            协方差矩阵 [n_trials, n_channels, n_channels]
        """
        try:
            return self.cov_estimator.transform(X)
        except Exception as e:
            logger.error(f"协方差矩阵计算失败: {e}")
            raise
    
    def get_reference_matrix(self) -> Optional[np.ndarray]:
        """
        获取参考矩阵（Riemannian均值）
        
        Returns:
            参考矩阵或None
        """
        return self._reference_matrix
    
    def get_performance_info(self) -> Dict[str, Any]:
        """
        获取性能信息
        
        Returns:
            性能信息字典
        """
        info = {
            'metric': self.metric,
            'estimator': self.estimator,
            'tangent_space': self.tangent_space,
            'n_features': self.n_channels * (self.n_channels + 1) // 2,
            'is_fitted': self._is_fitted
        }
        
        if self._reference_matrix is not None:
            info['reference_matrix_shape'] = self._reference_matrix.shape
        
        return info
