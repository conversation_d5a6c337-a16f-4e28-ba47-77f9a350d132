"""
数据验证工具函数

提供EEG数据质量检查和验证功能。
"""

import numpy as np
import scipy.stats as stats
from typing import Tuple, List, Dict, Optional, Any
import logging

logger = logging.getLogger(__name__)

def validate_eeg_data(data: np.ndarray, 
                     sampling_rate: float,
                     expected_channels: int = 8,
                     expected_duration: float = 2.0) -> Dict[str, Any]:
    """
    验证EEG数据的基本格式和质量
    
    Args:
        data: EEG数据 [n_trials, n_channels, n_samples] 或 [n_channels, n_samples]
        sampling_rate: 采样率 (Hz)
        expected_channels: 期望的通道数
        expected_duration: 期望的时长 (秒)
        
    Returns:
        验证结果字典
    """
    result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'info': {}
    }
    
    try:
        # 检查数据维度
        if data.ndim not in [2, 3]:
            result['valid'] = False
            result['errors'].append(f"数据维度错误: {data.ndim}，期望2或3维")
            return result
        
        if data.ndim == 2:
            n_channels, n_samples = data.shape
            n_trials = 1
        else:
            n_trials, n_channels, n_samples = data.shape
        
        result['info']['shape'] = data.shape
        result['info']['n_trials'] = n_trials
        result['info']['n_channels'] = n_channels
        result['info']['n_samples'] = n_samples
        
        # 检查通道数
        if n_channels != expected_channels:
            result['warnings'].append(
                f"通道数不匹配: {n_channels}，期望{expected_channels}"
            )
        
        # 检查采样点数和时长
        expected_samples = int(expected_duration * sampling_rate)
        actual_duration = n_samples / sampling_rate
        
        if abs(n_samples - expected_samples) > sampling_rate * 0.1:  # 允许0.1秒误差
            result['warnings'].append(
                f"时长不匹配: {actual_duration:.2f}秒，期望{expected_duration}秒"
            )
        
        result['info']['duration'] = actual_duration
        result['info']['sampling_rate'] = sampling_rate
        
        # 检查数据类型
        if not np.issubdtype(data.dtype, np.floating):
            result['warnings'].append(f"数据类型不是浮点型: {data.dtype}")
        
        # 检查数据范围（典型EEG信号范围）
        data_range = np.ptp(data)
        if data_range > 1000:  # 微伏
            result['warnings'].append(f"数据范围过大: {data_range:.2f}，可能需要缩放")
        elif data_range < 1:
            result['warnings'].append(f"数据范围过小: {data_range:.2f}，可能需要缩放")
        
        result['info']['data_range'] = data_range
        result['info']['data_min'] = np.min(data)
        result['info']['data_max'] = np.max(data)
        
        # 检查无效值
        n_nan = np.sum(np.isnan(data))
        n_inf = np.sum(np.isinf(data))
        
        if n_nan > 0:
            result['valid'] = False
            result['errors'].append(f"包含{n_nan}个NaN值")
        
        if n_inf > 0:
            result['valid'] = False
            result['errors'].append(f"包含{n_inf}个Inf值")
        
        result['info']['n_nan'] = n_nan
        result['info']['n_inf'] = n_inf
        
        logger.info(f"EEG数据验证完成: {result['info']}")
        
        return result
        
    except Exception as e:
        result['valid'] = False
        result['errors'].append(f"验证过程出错: {e}")
        logger.error(f"EEG数据验证失败: {e}")
        return result

def check_data_quality(data: np.ndarray, 
                      sampling_rate: float,
                      quality_thresholds: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
    """
    检查EEG数据质量
    
    Args:
        data: EEG数据 [n_trials, n_channels, n_samples] 或 [n_channels, n_samples]
        sampling_rate: 采样率 (Hz)
        quality_thresholds: 质量阈值字典
        
    Returns:
        质量评估结果
    """
    if quality_thresholds is None:
        quality_thresholds = {
            'max_amplitude': 200.0,  # 最大振幅 (μV)
            'min_variance': 0.1,     # 最小方差
            'max_variance': 1000.0,  # 最大方差
            'max_kurtosis': 10.0,    # 最大峰度
            'min_snr': 1.0          # 最小信噪比
        }
    
    result = {
        'overall_quality': 'good',
        'channel_quality': {},
        'trial_quality': {},
        'metrics': {},
        'issues': []
    }
    
    try:
        if data.ndim == 2:
            data = data[np.newaxis, ...]  # 添加试次维度
        
        n_trials, n_channels, n_samples = data.shape
        
        # 逐通道质量检查
        for ch in range(n_channels):
            ch_data = data[:, ch, :]
            ch_quality = {
                'amplitude': np.max(np.abs(ch_data)),
                'variance': np.var(ch_data),
                'kurtosis': stats.kurtosis(ch_data.flatten()),
                'skewness': stats.skew(ch_data.flatten()),
                'snr': _estimate_snr(ch_data, sampling_rate)
            }
            
            # 评估通道质量
            ch_issues = []
            if ch_quality['amplitude'] > quality_thresholds['max_amplitude']:
                ch_issues.append('amplitude_high')
            if ch_quality['variance'] < quality_thresholds['min_variance']:
                ch_issues.append('variance_low')
            if ch_quality['variance'] > quality_thresholds['max_variance']:
                ch_issues.append('variance_high')
            if abs(ch_quality['kurtosis']) > quality_thresholds['max_kurtosis']:
                ch_issues.append('kurtosis_high')
            if ch_quality['snr'] < quality_thresholds['min_snr']:
                ch_issues.append('snr_low')
            
            ch_quality['issues'] = ch_issues
            ch_quality['quality'] = 'poor' if len(ch_issues) > 2 else 'fair' if len(ch_issues) > 0 else 'good'
            
            result['channel_quality'][f'ch_{ch}'] = ch_quality
        
        # 逐试次质量检查
        for trial in range(n_trials):
            trial_data = data[trial]
            trial_quality = {
                'max_amplitude': np.max(np.abs(trial_data)),
                'mean_variance': np.mean(np.var(trial_data, axis=1)),
                'correlation_matrix': np.corrcoef(trial_data),
                'artifacts_detected': _detect_trial_artifacts(trial_data, sampling_rate)
            }
            
            # 评估试次质量
            trial_issues = []
            if trial_quality['max_amplitude'] > quality_thresholds['max_amplitude']:
                trial_issues.append('amplitude_high')
            if trial_quality['artifacts_detected']:
                trial_issues.append('artifacts')
            
            trial_quality['issues'] = trial_issues
            trial_quality['quality'] = 'poor' if len(trial_issues) > 1 else 'fair' if len(trial_issues) > 0 else 'good'
            
            result['trial_quality'][f'trial_{trial}'] = trial_quality
        
        # 整体质量评估
        channel_qualities = [ch['quality'] for ch in result['channel_quality'].values()]
        trial_qualities = [tr['quality'] for tr in result['trial_quality'].values()]
        
        poor_channels = channel_qualities.count('poor')
        poor_trials = trial_qualities.count('poor')
        
        if poor_channels > n_channels * 0.3 or poor_trials > n_trials * 0.3:
            result['overall_quality'] = 'poor'
        elif poor_channels > 0 or poor_trials > 0:
            result['overall_quality'] = 'fair'
        
        # 汇总指标
        result['metrics'] = {
            'n_poor_channels': poor_channels,
            'n_poor_trials': poor_trials,
            'mean_amplitude': np.mean([ch['amplitude'] for ch in result['channel_quality'].values()]),
            'mean_variance': np.mean([ch['variance'] for ch in result['channel_quality'].values()]),
            'mean_snr': np.mean([ch['snr'] for ch in result['channel_quality'].values()])
        }
        
        logger.info(f"数据质量检查完成: {result['overall_quality']}")
        
        return result
        
    except Exception as e:
        logger.error(f"数据质量检查失败: {e}")
        result['overall_quality'] = 'unknown'
        result['issues'].append(f"质量检查出错: {e}")
        return result

def detect_outliers(data: np.ndarray, 
                   method: str = 'iqr',
                   threshold: float = 3.0) -> Tuple[np.ndarray, np.ndarray]:
    """
    检测异常值
    
    Args:
        data: 输入数据 [n_trials, n_channels, n_samples]
        method: 检测方法 ('iqr', 'zscore', 'isolation_forest')
        threshold: 阈值
        
    Returns:
        (outlier_mask, outlier_scores): 异常值掩码和分数
    """
    try:
        if data.ndim != 3:
            raise ValueError("输入数据应为3维 [n_trials, n_channels, n_samples]")
        
        n_trials = data.shape[0]
        outlier_mask = np.zeros(n_trials, dtype=bool)
        outlier_scores = np.zeros(n_trials)
        
        if method == 'iqr':
            # 使用四分位距方法
            for trial in range(n_trials):
                trial_data = data[trial].flatten()
                q75, q25 = np.percentile(trial_data, [75, 25])
                iqr = q75 - q25
                lower_bound = q25 - threshold * iqr
                upper_bound = q75 + threshold * iqr
                
                outliers = (trial_data < lower_bound) | (trial_data > upper_bound)
                outlier_scores[trial] = np.sum(outliers) / len(trial_data)
                outlier_mask[trial] = outlier_scores[trial] > 0.05  # 5%异常值阈值
                
        elif method == 'zscore':
            # 使用Z分数方法
            for trial in range(n_trials):
                trial_data = data[trial].flatten()
                z_scores = np.abs(stats.zscore(trial_data))
                outlier_scores[trial] = np.max(z_scores)
                outlier_mask[trial] = outlier_scores[trial] > threshold
                
        else:
            raise ValueError(f"不支持的异常检测方法: {method}")
        
        logger.info(f"检测到{np.sum(outlier_mask)}个异常试次")
        
        return outlier_mask, outlier_scores
        
    except Exception as e:
        logger.error(f"异常值检测失败: {e}")
        raise

def validate_labels(labels: np.ndarray, 
                   expected_classes: List[int] = [0, 1]) -> Dict[str, Any]:
    """
    验证标签数据
    
    Args:
        labels: 标签数组
        expected_classes: 期望的类别列表
        
    Returns:
        验证结果
    """
    result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'info': {}
    }
    
    try:
        labels = np.array(labels)
        
        # 检查标签形状
        if labels.ndim != 1:
            result['valid'] = False
            result['errors'].append(f"标签应为1维数组，实际为{labels.ndim}维")
            return result
        
        result['info']['n_samples'] = len(labels)
        result['info']['unique_labels'] = np.unique(labels).tolist()
        
        # 检查标签类别
        unique_labels = np.unique(labels)
        for label in unique_labels:
            if label not in expected_classes:
                result['valid'] = False
                result['errors'].append(f"未知标签: {label}")
        
        # 检查类别平衡
        label_counts = {label: np.sum(labels == label) for label in expected_classes}
        result['info']['label_counts'] = label_counts
        
        min_count = min(label_counts.values())
        max_count = max(label_counts.values())
        
        if max_count > 0 and min_count / max_count < 0.3:
            result['warnings'].append("类别不平衡严重")
        
        # 检查标签连续性（避免过度聚集）
        if len(labels) > 10:
            # 计算标签变化次数
            changes = np.sum(np.diff(labels) != 0)
            expected_changes = len(labels) * 0.1  # 期望10%的变化
            
            if changes < expected_changes:
                result['warnings'].append("标签可能过度聚集")
        
        logger.info(f"标签验证完成: {result['info']}")
        
        return result
        
    except Exception as e:
        result['valid'] = False
        result['errors'].append(f"标签验证出错: {e}")
        logger.error(f"标签验证失败: {e}")
        return result

def _estimate_snr(data: np.ndarray, sampling_rate: float) -> float:
    """估算信噪比"""
    try:
        # 简单的SNR估算：信号功率 / 噪声功率
        # 假设高频部分主要是噪声
        from scipy import signal as sig
        
        # 低通滤波获取信号
        b, a = sig.butter(4, 30 / (sampling_rate / 2), 'low')
        signal_part = sig.filtfilt(b, a, data.flatten())
        
        # 高通滤波获取噪声
        b, a = sig.butter(4, 30 / (sampling_rate / 2), 'high')
        noise_part = sig.filtfilt(b, a, data.flatten())
        
        signal_power = np.var(signal_part)
        noise_power = np.var(noise_part)
        
        if noise_power > 0:
            snr = signal_power / noise_power
        else:
            snr = float('inf')
        
        return snr
        
    except Exception:
        return 1.0  # 默认SNR

def _detect_trial_artifacts(trial_data: np.ndarray, sampling_rate: float) -> bool:
    """检测单个试次的伪迹"""
    try:
        # 简单的伪迹检测：检查是否有异常大的振幅变化
        max_amplitude = np.max(np.abs(trial_data))
        amplitude_threshold = 200.0  # μV
        
        if max_amplitude > amplitude_threshold:
            return True
        
        # 检查是否有异常的梯度变化
        gradients = np.gradient(trial_data, axis=1)
        max_gradient = np.max(np.abs(gradients))
        gradient_threshold = 50.0  # μV/sample
        
        if max_gradient > gradient_threshold:
            return True
        
        return False
        
    except Exception:
        return False
