"""
5类算法组合验证脚本

使用EEG Motor Movement/Imagery Dataset验证5类算法组合的性能
包括：FBCSP+SVM, TEF+RF, Riemannian+MeanField, TangentSpace+LR, PLV+SVM
"""

import numpy as np
import os
import logging
from typing import Dict, List, Tuple, Any
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import time
import json

from .eeg_dataset_loader import EEGDatasetLoader
from ..feature_extraction.config import load_config
from ..feature_extraction.individual_feature_manager import IndividualFeatureManager
from ..classifier_training_manager import ClassifierTrainingManager
from ..weighted_voting_classifier import WeightedVotingManager

logger = logging.getLogger(__name__)

class AlgorithmValidator:
    """5类算法组合验证器"""
    
    def __init__(self, dataset_path: str, results_dir: str = "validation_results", config_path: str = None, enable_preprocessing: bool = True):
        """
        初始化验证器

        Args:
            dataset_path: EEG数据集路径
            results_dir: 结果保存目录
            config_path: 配置文件路径，如果为None则使用默认配置
            enable_preprocessing: 是否启用预处理管道
        """
        self.dataset_path = dataset_path
        self.results_dir = results_dir
        self.config_path = config_path
        self.enable_preprocessing = enable_preprocessing
        os.makedirs(results_dir, exist_ok=True)

        # 初始化数据加载器（集成预处理管道）
        self.data_loader = EEGDatasetLoader(dataset_path, enable_preprocessing=enable_preprocessing)

        # 初始化特征提取和分类器管理器
        if config_path:
            config = load_config(config_path)
        else:
            from utils.path_manager import get_config_file_in_dir
            balanced_config_path = str(get_config_file_in_dir('feature_extraction_balanced.json'))
            config = load_config(balanced_config_path)

        self.feature_manager = IndividualFeatureManager(config)
        self.classifier_manager = ClassifierTrainingManager(config_path)

        # 为验证环境添加PLV算法支持（不影响正常治疗逻辑）
        self._add_plv_algorithm_support()

        # 验证结果存储
        self.validation_results = {}

        logger.info(f"算法验证器初始化完成，使用配置: {config_path or 'default'}")
        logger.info(f"预处理管道: {'启用' if enable_preprocessing else '禁用'}")

    def _add_plv_algorithm_support(self):
        """为验证环境添加PLV算法支持（不影响正常治疗逻辑）"""
        try:
            # 检查PLV算法是否已存在
            if 'plv_svm' not in self.classifier_manager.classifier_configs:
                # 从原始配置文件加载PLV+SVM配置
                from sklearn.svm import SVC

                # 创建PLV+SVM分类器（使用原始配置）
                plv_svm_classifier = SVC(
                    C=1.0,
                    kernel='rbf',
                    gamma='auto',
                    probability=True,
                    random_state=42,
                    class_weight='balanced'
                )

                # 添加到分类器配置中
                self.classifier_manager.classifier_configs['plv_svm'] = {
                    'classifier': plv_svm_classifier,
                    'feature_type': 'plv'
                }

                logger.info("已为验证环境添加PLV+SVM算法支持（原始配置）")
            else:
                logger.info("PLV+SVM算法已存在于配置中")

        except Exception as e:
            logger.warning(f"添加PLV算法支持失败: {e}")

    def validate_single_subject(self, subject_id: str,
                               cv_folds: int = 5) -> Dict[str, Any]:
        """
        验证单个受试者的算法性能
        
        Args:
            subject_id: 受试者ID
            cv_folds: 交叉验证折数
            
        Returns:
            验证结果字典
        """
        try:
            logger.info(f"开始验证受试者 {subject_id}")
            
            # 加载数据
            X, y = self.data_loader.load_subject_data(subject_id)
            
            if len(np.unique(y)) < 2:
                raise ValueError(f"标签类别不足: {np.unique(y)}")
            
            # 训练特征提取器
            logger.info("训练特征提取器...")
            self.feature_manager.fit_and_save(X, y, f"validation_{subject_id}", "test")
            
            # 获取特征提取器
            feature_extractors = {}
            for name in self.feature_manager.get_enabled_extractors():
                feature_extractors[name] = self.feature_manager._extractors[name]
            
            # 验证各个算法
            results = {
                'subject_id': subject_id,
                'data_shape': X.shape,
                'label_distribution': np.bincount(y).tolist(),
                'algorithms': {},
                'voting_system': {}
            }
            
            # 单独验证每个算法
            for name, config in self.classifier_manager.classifier_configs.items():
                try:
                    feature_type = config['feature_type']
                    classifier = config['classifier']
                    
                    if feature_type not in feature_extractors:
                        logger.warning(f"跳过 {name}: 特征提取器 {feature_type} 不可用")
                        continue
                    
                    # 提取特征
                    features = feature_extractors[feature_type].transform(X)
                    
                    # 特殊处理Riemannian分类器
                    if feature_type == 'riemannian' and self.classifier_manager._is_riemannian_classifier(classifier):
                        features = self.classifier_manager._get_covariance_matrices(X)
                    else:
                        features = self.classifier_manager._clean_features(features, feature_type)
                    
                    # 交叉验证
                    cv_scores = cross_val_score(
                        classifier, features, y, 
                        cv=StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42),
                        scoring='accuracy'
                    )
                    
                    # 训练完整模型评估
                    classifier.fit(features, y)
                    train_accuracy = accuracy_score(y, classifier.predict(features))
                    
                    results['algorithms'][name] = {
                        'feature_type': feature_type,
                        'feature_shape': features.shape,
                        'cv_scores': cv_scores.tolist(),
                        'cv_mean': float(np.mean(cv_scores)),
                        'cv_std': float(np.std(cv_scores)),
                        'train_accuracy': float(train_accuracy)
                    }
                    
                    logger.info(f"{name}: CV准确率 {np.mean(cv_scores):.3f}±{np.std(cv_scores):.3f}")
                    
                except Exception as e:
                    logger.error(f"算法 {name} 验证失败: {e}")
                    results['algorithms'][name] = {'error': str(e)}
            
            # 验证投票系统
            try:
                voting_results = self._validate_voting_system(X, y, feature_extractors, cv_folds)
                results['voting_system'] = voting_results
            except Exception as e:
                logger.error(f"投票系统验证失败: {e}")
                results['voting_system'] = {'error': str(e)}
            
            # 保存结果
            self._save_subject_results(subject_id, results)
            
            logger.info(f"受试者 {subject_id} 验证完成")
            return results
            
        except Exception as e:
            logger.error(f"受试者 {subject_id} 验证失败: {e}")
            return {'subject_id': subject_id, 'error': str(e)}
    
    def _validate_voting_system(self, X: np.ndarray, y: np.ndarray,
                               feature_extractors: Dict, cv_folds: int) -> Dict[str, Any]:
        """验证加权投票系统性能"""
        try:
            logger.info("开始验证加权投票系统...")

            # 准备分类器数据
            classifiers_data = {}

            # 为每个分类器准备数据
            for name, config in self.classifier_manager.classifier_configs.items():
                try:
                    feature_type = config['feature_type']
                    classifier = config['classifier']

                    if feature_type not in feature_extractors:
                        logger.warning(f"跳过 {name}: 特征提取器 {feature_type} 不可用")
                        continue

                    # 提取特征
                    features = feature_extractors[feature_type].transform(X)

                    # 特殊处理Riemannian分类器
                    if feature_type == 'riemannian' and self.classifier_manager._is_riemannian_classifier(classifier):
                        features = self.classifier_manager._get_covariance_matrices(X)
                    else:
                        features = self.classifier_manager._clean_features(features, feature_type)

                    # 训练分类器并评估性能
                    cv_scores = cross_val_score(
                        classifier, features, y,
                        cv=StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42),
                        scoring='accuracy'
                    )

                    # 训练完整模型
                    classifier.fit(features, y)
                    train_accuracy = accuracy_score(y, classifier.predict(features))

                    # 创建性能对象
                    from services.weighted_voting_classifier import ClassifierPerformance
                    performance = ClassifierPerformance(
                        accuracy=train_accuracy,
                        cv_scores=cv_scores.tolist(),
                        cv_mean=float(np.mean(cv_scores)),
                        cv_std=float(np.std(cv_scores)),
                        training_time=0.0,  # 这里简化，不计算训练时间
                        n_samples=len(y)
                    )

                    classifiers_data[name] = {
                        'classifier': classifier,
                        'feature_extractor': feature_extractors[feature_type],
                        'performance': performance
                    }

                    logger.info(f"准备分类器 {name}: CV准确率 {np.mean(cv_scores):.3f}±{np.std(cv_scores):.3f}")

                except Exception as e:
                    logger.error(f"准备分类器 {name} 失败: {e}")
                    continue

            if not classifiers_data:
                return {'error': '没有可用的分类器'}

            # 使用嵌套交叉验证避免数据泄露
            logger.info("使用嵌套交叉验证验证Stacking系统（避免数据泄露）...")

            return self._validate_stacking_system_nested_cv(X, y, classifiers_data, cv_folds)

        except Exception as e:
            logger.error(f"加权投票系统验证失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {'error': str(e)}

    def _calculate_error_correlations(self, X: np.ndarray, y: np.ndarray,
                                    classifiers_data: Dict, cv_folds: int) -> np.ndarray:
        """计算分类器错误相关性矩阵"""
        try:
            classifier_names = list(classifiers_data.keys())
            n_classifiers = len(classifier_names)
            error_matrix = np.zeros((len(y), n_classifiers))

            # 使用交叉验证获取每个分类器的错误模式
            skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)

            for train_idx, test_idx in skf.split(X, y):
                X_train, X_test = X[train_idx], X[test_idx]
                y_train, y_test = y[train_idx], y[test_idx]

                for i, (name, data) in enumerate(classifiers_data.items()):
                    try:
                        # 训练分类器
                        feature_extractor = data['feature_extractor']
                        classifier = data['classifier']

                        temp_extractor = feature_extractor.__class__(**feature_extractor.get_params())
                        temp_extractor.fit(X_train, y_train)

                        config = self.classifier_manager.classifier_configs[name]
                        feature_type = config['feature_type']

                        if feature_type == 'riemannian' and self.classifier_manager._is_riemannian_classifier(classifier):
                            train_features = self.classifier_manager._get_covariance_matrices(X_train)
                            test_features = self.classifier_manager._get_covariance_matrices(X_test)
                        else:
                            train_features = temp_extractor.transform(X_train)
                            test_features = temp_extractor.transform(X_test)
                            train_features = self.classifier_manager._clean_features(train_features, feature_type)
                            test_features = self.classifier_manager._clean_features(test_features, feature_type)

                        temp_classifier = classifier.__class__(**classifier.get_params())
                        temp_classifier.fit(train_features, y_train)

                        # 预测并记录错误
                        predictions = temp_classifier.predict(test_features)
                        errors = (predictions != y_test).astype(int)
                        error_matrix[test_idx, i] = errors

                    except Exception as e:
                        logger.warning(f"分类器 {name} 错误相关性计算失败: {e}")
                        error_matrix[test_idx, i] = 1  # 假设全部错误

            # 计算错误相关性矩阵
            correlation_matrix = np.corrcoef(error_matrix.T)
            correlation_matrix = np.nan_to_num(correlation_matrix, nan=0.0)

            logger.info(f"错误相关性矩阵计算完成，形状: {correlation_matrix.shape}")
            return correlation_matrix

        except Exception as e:
            logger.error(f"错误相关性计算失败: {e}")
            return np.eye(len(classifiers_data))  # 返回单位矩阵作为默认值

    def _select_diverse_classifiers(self, classifiers_data: Dict,
                                  error_correlations: np.ndarray) -> Dict:
        """基于错误相关性和性能选择多样化的分类器组合"""
        try:
            classifier_names = list(classifiers_data.keys())
            n_classifiers = len(classifier_names)

            # 获取性能排序
            performance_scores = [(name, data['performance'].cv_mean)
                                for name, data in classifiers_data.items()]
            performance_scores.sort(key=lambda x: x[1], reverse=True)

            # 选择策略：从最佳分类器开始，选择与已选分类器错误相关性低的分类器
            selected_classifiers = {}
            selected_indices = []

            # 首先选择最佳分类器
            best_name, best_score = performance_scores[0]
            selected_classifiers[best_name] = classifiers_data[best_name]
            selected_indices.append(classifier_names.index(best_name))

            logger.info(f"选择最佳分类器: {best_name} (性能: {best_score:.3f})")

            # 选择与已选分类器错误相关性最低的分类器
            correlation_threshold = 0.7  # 相关性阈值

            for name, score in performance_scores[1:]:
                current_idx = classifier_names.index(name)

                # 计算与已选分类器的平均相关性
                avg_correlation = np.mean([abs(error_correlations[current_idx, selected_idx])
                                         for selected_idx in selected_indices])

                if avg_correlation < correlation_threshold:
                    selected_classifiers[name] = classifiers_data[name]
                    selected_indices.append(current_idx)
                    logger.info(f"选择多样化分类器: {name} (性能: {score:.3f}, 平均相关性: {avg_correlation:.3f})")
                else:
                    logger.info(f"跳过高相关性分类器: {name} (平均相关性: {avg_correlation:.3f})")

            # 确保至少有2个分类器
            if len(selected_classifiers) < 2:
                logger.warning("选择的分类器数量不足，添加次佳分类器")
                for name, score in performance_scores[1:]:
                    if name not in selected_classifiers:
                        selected_classifiers[name] = classifiers_data[name]
                        break

            logger.info(f"最终选择的分类器: {list(selected_classifiers.keys())}")
            return selected_classifiers

        except Exception as e:
            logger.error(f"多样化分类器选择失败: {e}")
            return classifiers_data  # 返回所有分类器作为默认值

    def _train_stacking_ensemble(self, X: np.ndarray, y: np.ndarray,
                               selected_classifiers: Dict, cv_folds: int) -> Dict[str, Any]:
        """训练Stacking集成模型"""
        try:
            from sklearn.ensemble import StackingClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.model_selection import cross_val_score

            logger.info("开始训练Stacking集成模型...")

            # 准备基分类器列表
            base_classifiers = []

            for name, data in selected_classifiers.items():
                try:
                    feature_extractor = data['feature_extractor']
                    classifier = data['classifier']
                    config = self.classifier_manager.classifier_configs[name]
                    feature_type = config['feature_type']

                    # 创建包装器来处理特征提取
                    from sklearn.pipeline import Pipeline

                    if feature_type == 'riemannian' and self.classifier_manager._is_riemannian_classifier(classifier):
                        # 对于Riemannian分类器，需要特殊处理
                        # 这里简化处理，直接使用分类器
                        base_classifiers.append((name, classifier))
                    else:
                        # 创建Pipeline包含特征提取和分类
                        pipeline = Pipeline([
                            ('feature_extractor', feature_extractor),
                            ('classifier', classifier)
                        ])
                        base_classifiers.append((name, pipeline))

                    logger.info(f"添加基分类器: {name}")

                except Exception as e:
                    logger.warning(f"基分类器 {name} 准备失败: {e}")
                    continue

            if len(base_classifiers) < 2:
                logger.error("可用的基分类器数量不足")
                return {'error': '可用的基分类器数量不足'}

            # 创建Stacking分类器，使用逻辑回归作为元分类器
            meta_classifier = LogisticRegression(random_state=42, max_iter=1000)
            stacking_clf = StackingClassifier(
                estimators=base_classifiers,
                final_estimator=meta_classifier,
                cv=cv_folds,
                stack_method='predict_proba',
                n_jobs=-1
            )

            # 交叉验证评估Stacking模型
            cv_scores = cross_val_score(
                stacking_clf, X, y,
                cv=StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42),
                scoring='accuracy',
                n_jobs=-1
            )

            # 训练完整模型
            stacking_clf.fit(X, y)
            train_accuracy = stacking_clf.score(X, y)

            result = {
                'cv_scores': cv_scores.tolist(),
                'cv_mean': float(np.mean(cv_scores)),
                'cv_std': float(np.std(cv_scores)),
                'train_accuracy': float(train_accuracy),
                'n_classifiers': len(base_classifiers),
                'classifier_names': [name for name, _ in base_classifiers],
                'method': 'stacking_ensemble',
                'meta_classifier': 'LogisticRegression'
            }

            logger.info(f"Stacking集成模型训练完成: CV准确率 {np.mean(cv_scores):.3f}±{np.std(cv_scores):.3f}")
            return result

        except Exception as e:
            logger.error(f"Stacking集成训练失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {'error': str(e)}

    def _train_optimized_voting_ensemble(self, X: np.ndarray, y: np.ndarray,
                                       selected_classifiers: Dict, cv_folds: int) -> Dict[str, Any]:
        """训练优化的加权投票集成模型"""
        try:
            logger.info("开始训练优化的加权投票集成模型...")

            # 计算基于多样性和性能的优化权重
            optimized_weights = self._calculate_optimized_weights(selected_classifiers)

            # 交叉验证优化投票系统
            cv_scores = []
            cv_details = []
            skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)

            for fold_idx, (train_idx, test_idx) in enumerate(skf.split(X, y)):
                X_train, X_test = X[train_idx], X[test_idx]
                y_train, y_test = y[train_idx], y[test_idx]

                # 为每个fold训练分类器
                fold_predictions = {}
                fold_weights = {}

                for name, data in selected_classifiers.items():
                    try:
                        # 训练分类器
                        feature_extractor = data['feature_extractor']
                        classifier = data['classifier']

                        temp_extractor = feature_extractor.__class__(**feature_extractor.get_params())
                        temp_extractor.fit(X_train, y_train)

                        config = self.classifier_manager.classifier_configs[name]
                        feature_type = config['feature_type']

                        if feature_type == 'riemannian' and self.classifier_manager._is_riemannian_classifier(classifier):
                            train_features = self.classifier_manager._get_covariance_matrices(X_train)
                            test_features = self.classifier_manager._get_covariance_matrices(X_test)
                        else:
                            train_features = temp_extractor.transform(X_train)
                            test_features = temp_extractor.transform(X_test)
                            train_features = self.classifier_manager._clean_features(train_features, feature_type)
                            test_features = self.classifier_manager._clean_features(test_features, feature_type)

                        temp_classifier = classifier.__class__(**classifier.get_params())
                        temp_classifier.fit(train_features, y_train)

                        # 获取预测概率
                        if hasattr(temp_classifier, 'predict_proba'):
                            pred_proba = temp_classifier.predict_proba(test_features)
                            predictions = pred_proba[:, 1]  # 取正类概率
                        else:
                            predictions = temp_classifier.predict(test_features).astype(float)

                        fold_predictions[name] = predictions
                        fold_weights[name] = optimized_weights[name]

                        logger.info(f"Fold {fold_idx} 分类器 {name} 训练成功")

                    except Exception as e:
                        logger.warning(f"Fold {fold_idx} 分类器 {name} 训练失败: {e}")
                        continue

                if not fold_predictions:
                    logger.warning(f"Fold {fold_idx} 没有可用的分类器")
                    continue

                # 加权投票预测
                ensemble_predictions = np.zeros(len(y_test))
                total_weight = sum(fold_weights.values())

                for name, predictions in fold_predictions.items():
                    weight = fold_weights[name] / total_weight
                    ensemble_predictions += weight * predictions

                # 转换为二分类预测
                final_predictions = (ensemble_predictions > 0.5).astype(int)
                fold_accuracy = accuracy_score(y_test, final_predictions)
                cv_scores.append(fold_accuracy)

                cv_details.append({
                    'fold': fold_idx,
                    'accuracy': fold_accuracy,
                    'n_test_samples': len(y_test),
                    'participating_classifiers': list(fold_predictions.keys()),
                    'weights': fold_weights.copy()
                })

                logger.info(f"Fold {fold_idx}: 准确率 {fold_accuracy:.3f}, 参与分类器: {len(fold_predictions)}")

            # 计算最终结果
            if cv_scores:
                result = {
                    'cv_scores': cv_scores,
                    'cv_mean': float(np.mean(cv_scores)),
                    'cv_std': float(np.std(cv_scores)),
                    'n_classifiers': len(selected_classifiers),
                    'classifier_names': list(selected_classifiers.keys()),
                    'optimized_weights': optimized_weights.copy(),
                    'method': 'optimized_weighted_voting',
                    'cv_details': cv_details
                }

                logger.info(f"优化加权投票集成训练完成: CV准确率 {np.mean(cv_scores):.3f}±{np.std(cv_scores):.3f}")
                return result
            else:
                return {'error': '交叉验证失败，没有有效的fold结果'}

        except Exception as e:
            logger.error(f"优化加权投票集成训练失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {'error': str(e)}

    def _calculate_optimized_weights(self, selected_classifiers: Dict) -> Dict[str, float]:
        """计算基于性能和多样性的优化权重"""
        try:
            # 获取性能分数
            performance_scores = {name: data['performance'].cv_mean
                                for name, data in selected_classifiers.items()}

            # 方法：基于性能的平方根权重（减少最佳分类器的主导地位）
            sqrt_scores = {name: np.sqrt(score) for name, score in performance_scores.items()}
            total_sqrt = sum(sqrt_scores.values())

            optimized_weights = {name: score / total_sqrt for name, score in sqrt_scores.items()}

            logger.info(f"优化权重计算完成: {optimized_weights}")
            return optimized_weights

        except Exception as e:
            logger.error(f"优化权重计算失败: {e}")
            # 返回均等权重作为默认值
            n_classifiers = len(selected_classifiers)
            return {name: 1.0 / n_classifiers for name in selected_classifiers.keys()}

    def _validate_stacking_system_nested_cv(self, X: np.ndarray, y: np.ndarray,
                                           classifiers_data: Dict, cv_folds: int) -> Dict[str, Any]:
        """使用嵌套交叉验证验证Stacking系统（避免数据泄露）"""
        try:
            logger.info("开始嵌套交叉验证Stacking系统...")

            cv_scores = []
            cv_details = []
            skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)

            for fold_idx, (train_idx, test_idx) in enumerate(skf.split(X, y)):
                X_train, X_test = X[train_idx], X[test_idx]
                y_train, y_test = y[train_idx], y[test_idx]

                try:
                    # 在训练集上重新训练所有组件
                    fold_classifiers_data = self._retrain_classifiers_on_fold(
                        X_train, y_train, classifiers_data
                    )

                    # 在训练集上训练Stacking系统
                    voting_manager = WeightedVotingManager()
                    voting_manager.fit(X_train, y_train, fold_classifiers_data)

                    # 在测试集上预测
                    fold_predictions = []
                    for i in range(len(X_test)):
                        sample = X_test[i:i+1]  # 单个样本
                        result = voting_manager.predict_with_details(sample)
                        fold_predictions.append(result['final_prediction'])

                    fold_predictions = np.array(fold_predictions)
                    fold_accuracy = accuracy_score(y_test, fold_predictions)
                    cv_scores.append(fold_accuracy)

                    cv_details.append({
                        'fold': fold_idx,
                        'accuracy': fold_accuracy,
                        'n_test_samples': len(y_test),
                        'n_train_samples': len(y_train),
                        'method': 'stacking_nested_cv',
                        'selected_classifiers': list(voting_manager.voting_system.selected_classifiers.keys()) if hasattr(voting_manager.voting_system, 'selected_classifiers') else []
                    })

                    logger.info(f"Fold {fold_idx}: Stacking准确率 {fold_accuracy:.3f} (训练:{len(y_train)}, 测试:{len(y_test)})")

                except Exception as e:
                    logger.warning(f"Fold {fold_idx} Stacking训练/预测失败: {e}")
                    continue

            if cv_scores:
                result = {
                    'cv_scores': cv_scores,
                    'cv_mean': float(np.mean(cv_scores)),
                    'cv_std': float(np.std(cv_scores)),
                    'method': 'stacking_nested_cv',
                    'cv_details': cv_details,
                    'data_leakage_fixed': True
                }

                logger.info(f"嵌套CV Stacking验证完成: CV准确率 {np.mean(cv_scores):.3f}±{np.std(cv_scores):.3f}")
                return result
            else:
                return {'error': '所有fold验证失败'}

        except Exception as e:
            logger.error(f"嵌套交叉验证失败: {e}")
            return {'error': str(e)}

    def _retrain_classifiers_on_fold(self, X_train: np.ndarray, y_train: np.ndarray,
                                    original_classifiers_data: Dict) -> Dict:
        """在fold训练集上重新训练分类器"""
        fold_classifiers_data = {}

        for name, data in original_classifiers_data.items():
            try:
                # 获取原始配置
                original_classifier = data['classifier']
                feature_extractor = data['feature_extractor']

                # 创建新的分类器实例
                classifier = original_classifier.__class__(**original_classifier.get_params())

                # 重新训练特征提取器
                temp_extractor = feature_extractor.__class__(**feature_extractor.get_params())
                temp_extractor.fit(X_train, y_train)

                # 提取特征
                features = temp_extractor.transform(X_train)

                # 特殊处理Riemannian分类器
                if hasattr(self.classifier_manager, '_is_riemannian_classifier') and \
                   self.classifier_manager._is_riemannian_classifier(classifier):
                    features = self.classifier_manager._get_covariance_matrices(X_train)
                else:
                    features = self.classifier_manager._clean_features(features, name.split('_')[0])

                # 训练分类器
                classifier.fit(features, y_train)

                # 评估性能（在训练集上，用于筛选）
                cv_scores = cross_val_score(
                    classifier, features, y_train,
                    cv=min(3, len(np.unique(y_train))),  # 最多3折
                    scoring='accuracy'
                )

                # 创建性能对象
                from services.weighted_voting_classifier import ClassifierPerformance
                performance = ClassifierPerformance(
                    accuracy=accuracy_score(y_train, classifier.predict(features)),
                    cv_scores=cv_scores.tolist(),
                    cv_mean=float(np.mean(cv_scores)),
                    cv_std=float(np.std(cv_scores)),
                    training_time=0.0,
                    n_samples=len(y_train)
                )

                fold_classifiers_data[name] = {
                    'classifier': classifier,
                    'feature_extractor': temp_extractor,
                    'performance': performance
                }

            except Exception as e:
                logger.warning(f"Fold训练分类器 {name} 失败: {e}")
                continue

        return fold_classifiers_data

    def _validate_stacking_system(self, X: np.ndarray, y: np.ndarray,
                                voting_manager, cv_folds: int) -> Dict[str, Any]:
        """验证训练好的Stacking系统"""
        try:
            logger.info("开始验证Stacking系统性能...")

            cv_scores = []
            cv_details = []
            skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)

            for fold_idx, (train_idx, test_idx) in enumerate(skf.split(X, y)):
                X_test = X[test_idx]
                y_test = y[test_idx]

                # 使用训练好的Stacking系统进行预测
                try:
                    fold_predictions = []
                    for i in range(len(X_test)):
                        sample = X_test[i:i+1]  # 单个样本
                        result = voting_manager.predict_with_details(sample)
                        fold_predictions.append(result['final_prediction'])

                    fold_predictions = np.array(fold_predictions)
                    fold_accuracy = accuracy_score(y_test, fold_predictions)
                    cv_scores.append(fold_accuracy)

                    cv_details.append({
                        'fold': fold_idx,
                        'accuracy': fold_accuracy,
                        'n_test_samples': len(y_test),
                        'method': 'stacking',
                        'selected_classifiers': list(voting_manager.voting_system.selected_classifiers.keys()) if hasattr(voting_manager.voting_system, 'selected_classifiers') else []
                    })

                    logger.info(f"Fold {fold_idx}: Stacking准确率 {fold_accuracy:.3f}")

                except Exception as e:
                    logger.warning(f"Fold {fold_idx} Stacking预测失败: {e}")
                    continue

            if cv_scores:
                result = {
                    'cv_scores': cv_scores,
                    'cv_mean': float(np.mean(cv_scores)),
                    'cv_std': float(np.std(cv_scores)),
                    'n_classifiers': len(voting_manager.voting_system.selected_classifiers) if hasattr(voting_manager.voting_system, 'selected_classifiers') else len(voting_manager.voting_system.classifiers),
                    'classifier_names': list(voting_manager.voting_system.selected_classifiers.keys()) if hasattr(voting_manager.voting_system, 'selected_classifiers') else list(voting_manager.voting_system.classifiers.keys()),
                    'method': 'stacking_ensemble',
                    'meta_classifier_performance': getattr(voting_manager.voting_system, 'meta_classifier', None) is not None,
                    'cv_details': cv_details
                }

                logger.info(f"Stacking系统验证完成: CV准确率 {np.mean(cv_scores):.3f}±{np.std(cv_scores):.3f}")
                return result
            else:
                return {'error': 'Stacking系统验证失败，没有有效的fold结果'}

        except Exception as e:
            logger.error(f"Stacking系统验证失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {'error': str(e)}
    
    def validate_multiple_subjects(self, subject_ids: List[str], 
                                  cv_folds: int = 5) -> Dict[str, Any]:
        """
        验证多个受试者
        
        Args:
            subject_ids: 受试者ID列表
            cv_folds: 交叉验证折数
            
        Returns:
            汇总验证结果
        """
        all_results = []
        summary_stats = {
            'algorithms': {},
            'voting_system': {},
            'subjects_processed': 0,
            'subjects_failed': 0
        }
        
        for subject_id in subject_ids:
            try:
                result = self.validate_single_subject(subject_id, cv_folds)
                if 'error' not in result:
                    all_results.append(result)
                    summary_stats['subjects_processed'] += 1
                else:
                    summary_stats['subjects_failed'] += 1
                    logger.error(f"受试者 {subject_id} 验证失败: {result['error']}")
            except Exception as e:
                summary_stats['subjects_failed'] += 1
                logger.error(f"受试者 {subject_id} 验证异常: {e}")
        
        # 计算汇总统计
        if all_results:
            summary_stats = self._compute_summary_statistics(all_results)
        
        # 保存汇总结果
        self._save_summary_results(summary_stats, subject_ids)
        
        return summary_stats
    
    def _compute_summary_statistics(self, all_results: List[Dict]) -> Dict[str, Any]:
        """计算汇总统计"""
        summary = {
            'subjects_processed': len(all_results),
            'algorithms': {},
            'voting_system': {}
        }
        
        # 汇总各算法性能
        algorithm_names = set()
        for result in all_results:
            algorithm_names.update(result['algorithms'].keys())
        
        for alg_name in algorithm_names:
            cv_means = []
            cv_stds = []
            train_accs = []
            
            for result in all_results:
                if alg_name in result['algorithms'] and 'cv_mean' in result['algorithms'][alg_name]:
                    cv_means.append(result['algorithms'][alg_name]['cv_mean'])
                    cv_stds.append(result['algorithms'][alg_name]['cv_std'])
                    train_accs.append(result['algorithms'][alg_name]['train_accuracy'])
            
            if cv_means:
                summary['algorithms'][alg_name] = {
                    'mean_cv_accuracy': float(np.mean(cv_means)),
                    'std_cv_accuracy': float(np.std(cv_means)),
                    'mean_train_accuracy': float(np.mean(train_accs)),
                    'n_subjects': len(cv_means)
                }
        
        # 汇总投票系统性能
        voting_cv_means = []
        for result in all_results:
            if 'cv_mean' in result['voting_system']:
                voting_cv_means.append(result['voting_system']['cv_mean'])
        
        if voting_cv_means:
            summary['voting_system'] = {
                'mean_cv_accuracy': float(np.mean(voting_cv_means)),
                'std_cv_accuracy': float(np.std(voting_cv_means)),
                'n_subjects': len(voting_cv_means)
            }
        
        return summary
    
    def _save_subject_results(self, subject_id: str, results: Dict):
        """保存单个受试者结果"""
        filename = os.path.join(self.results_dir, f"{subject_id}_results.json")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
    
    def _save_summary_results(self, summary: Dict, subject_ids: List[str]):
        """保存汇总结果"""
        summary['validation_info'] = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'subject_ids': subject_ids,
            'dataset_path': self.dataset_path
        }
        
        filename = os.path.join(self.results_dir, "validation_summary.json")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"验证结果已保存到: {filename}")
