"""
TEF (Time-Entropy-Frequency) 特征提取器

实现时域-熵域-频域多域特征提取。
"""

import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
import warnings

try:
    import scipy.stats as stats
    from scipy.fft import fft, fftfreq
    from scipy.signal import welch
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    stats = None
    fft = None
    fftfreq = None
    welch = None

from .base_extractor import BaseFeatureExtractor

logger = logging.getLogger(__name__)

class TEFExtractor(BaseFeatureExtractor):
    """
    TEF特征提取器
    
    提取时域、熵域、频域的多种特征，形成高维特征向量。
    """
    
    def __init__(self,
                 time_features: Optional[Dict[str, bool]] = None,
                 entropy_features: Optional[Dict[str, bool]] = None,
                 freq_features: Optional[Dict[str, bool]] = None,
                 freq_bands: Optional[List[Tuple[float, float]]] = None,
                 nperseg: int = 125,
                 feature_selection: Optional[Dict] = None,
                 **kwargs):
        """
        初始化TEF特征提取器
        
        Args:
            time_features: 时域特征开关字典
            entropy_features: 熵域特征开关字典
            freq_features: 频域特征开关字典
            freq_bands: 频段定义
            nperseg: FFT窗口长度
        """
        super().__init__(**kwargs)
        
        if not SCIPY_AVAILABLE:
            raise ImportError("TEF需要安装scipy: pip install scipy")
        
        # 默认特征配置
        if time_features is None:
            time_features = {
                'mean': True, 'std': True, 'var': True, 'skew': True,
                'kurtosis': True, 'rms': True, 'peak': True, 'energy': True
            }
        
        if entropy_features is None:
            entropy_features = {
                'sample_entropy': True, 'approximate_entropy': True,
                'permutation_entropy': True, 'fuzzy_entropy': False,
                'multiscale_entropy': False
            }
        
        if freq_features is None:
            freq_features = {
                'power_spectral_density': True, 'spectral_centroid': True,
                'spectral_bandwidth': True, 'spectral_rolloff': True,
                'spectral_flatness': True, 'spectral_crest': True,
                'dominant_frequency': True, 'frequency_variance': True
            }
        
        if freq_bands is None:
            freq_bands = [
                (0.5, 4),   # Delta
                (4, 8),     # Theta  
                (8, 13),    # Alpha
                (13, 30),   # Beta
                (30, 50)    # Gamma
            ]
        
        self.time_features = time_features
        self.entropy_features = entropy_features
        self.freq_features = freq_features
        self.freq_bands = freq_bands
        self.nperseg = nperseg

        # 🔧 特征选择配置
        self.feature_selection_config = feature_selection or {}
        self.feature_selector = None
        self.channel_selector = None

        # 🔧 禁用通道选择器 - TEF应该在数据预处理阶段选择通道
        # 通道选择器假设特征按通道均匀分布，但TEF特征是按特征类型组织的
        self.channel_selector = None

        # 计算特征维度
        self._calculate_feature_dimensions()

        logger.info(f"TEF初始化完成，总特征维度: {self.total_features}")
        if self.feature_selection_config.get('enabled'):
            logger.info(f"特征选择已启用，方法: {self.feature_selection_config.get('method', 'mrmr')}")
    
    def _calculate_feature_dimensions(self):
        """计算特征维度"""
        self.time_dim = sum(self.time_features.values()) * self.n_channels
        self.entropy_dim = sum(self.entropy_features.values()) * self.n_channels
        self.freq_dim = sum(self.freq_features.values()) * self.n_channels
        self.total_features = self.time_dim + self.entropy_dim + self.freq_dim
    
    def _fit(self, X: np.ndarray, y: np.ndarray) -> 'TEFExtractor':
        """
        拟合TEF模型（包含特征选择训练）

        Args:
            X: 训练数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]

        Returns:
            self
        """
        try:
            # 🔧 如果启用特征选择，需要先提取特征然后训练选择器
            if self.feature_selection_config.get('enabled'):
                # 提取所有特征
                features = self._transform(X)

                # 应用通道选择
                if self.channel_selector:
                    features = self.channel_selector.apply_channel_selection(features, self.n_channels)

                # 训练特征选择器
                from .feature_selector import create_feature_selector
                self.feature_selector = create_feature_selector(self.feature_selection_config)

                if self.feature_selector:
                    self.feature_selector.fit(features, y)
                    selected_features = self.feature_selector.get_selected_features()
                    logger.info(f"TEF特征选择完成，从{features.shape[1]}个特征中选择了{len(selected_features)}个")

            logger.info(f"TEF特征提取器拟合完成，数据形状: {X.shape}")
            return self

        except Exception as e:
            logger.error(f"TEF特征提取器拟合失败: {e}")
            # 如果特征选择失败，禁用特征选择继续运行
            self.feature_selector = None
            logger.warning("特征选择失败，将使用所有特征")
            return self
    
    def _transform(self, X: np.ndarray) -> np.ndarray:
        """
        提取TEF特征
        
        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]
            
        Returns:
            TEF特征 [n_trials, n_features]
        """
        try:
            n_trials = X.shape[0]
            all_features = []
            
            for trial in range(n_trials):
                trial_data = X[trial]  # [n_channels, n_samples]
                
                # 提取各域特征
                time_feat = self._extract_time_features(trial_data)
                entropy_feat = self._extract_entropy_features(trial_data)
                freq_feat = self._extract_frequency_features(trial_data)
                
                # 拼接特征
                trial_features = np.concatenate([time_feat, entropy_feat, freq_feat])
                all_features.append(trial_features)
            
            features = np.array(all_features)

            # 🔧 应用通道选择
            if self.channel_selector:
                features = self.channel_selector.apply_channel_selection(features, self.n_channels)
                logger.debug(f"通道选择后特征维度: {features.shape[1]}")

            # 🔧 应用特征选择
            if self.feature_selector:
                features = self.feature_selector.transform(features)
                logger.debug(f"特征选择后特征维度: {features.shape[1]}")

            logger.debug(f"TEF特征提取完成，最终特征维度: {features.shape[1]}")

            return features
            
        except Exception as e:
            logger.error(f"TEF特征提取失败: {e}")
            raise
    
    def _extract_time_features(self, data: np.ndarray) -> np.ndarray:
        """
        提取时域特征
        
        Args:
            data: 单个试次数据 [n_channels, n_samples]
            
        Returns:
            时域特征向量
        """
        features = []
        
        for ch in range(data.shape[0]):
            ch_data = data[ch]
            ch_features = []
            
            if self.time_features.get('mean', False):
                ch_features.append(np.mean(ch_data))
            
            if self.time_features.get('std', False):
                ch_features.append(np.std(ch_data))
            
            if self.time_features.get('var', False):
                ch_features.append(np.var(ch_data))
            
            if self.time_features.get('skew', False):
                ch_features.append(stats.skew(ch_data))
            
            if self.time_features.get('kurtosis', False):
                ch_features.append(stats.kurtosis(ch_data))
            
            if self.time_features.get('rms', False):
                ch_features.append(np.sqrt(np.mean(ch_data ** 2)))
            
            if self.time_features.get('peak', False):
                ch_features.append(np.max(np.abs(ch_data)))
            
            if self.time_features.get('energy', False):
                ch_features.append(np.sum(ch_data ** 2))
            
            features.extend(ch_features)
        
        return np.array(features)
    
    def _extract_entropy_features(self, data: np.ndarray) -> np.ndarray:
        """
        提取熵域特征
        
        Args:
            data: 单个试次数据 [n_channels, n_samples]
            
        Returns:
            熵域特征向量
        """
        features = []
        
        for ch in range(data.shape[0]):
            ch_data = data[ch]
            ch_features = []
            
            if self.entropy_features.get('sample_entropy', False):
                ch_features.append(self._sample_entropy(ch_data))
            
            if self.entropy_features.get('approximate_entropy', False):
                ch_features.append(self._approximate_entropy(ch_data))
            
            if self.entropy_features.get('permutation_entropy', False):
                ch_features.append(self._permutation_entropy(ch_data))
            
            if self.entropy_features.get('fuzzy_entropy', False):
                ch_features.append(self._fuzzy_entropy(ch_data))
            
            if self.entropy_features.get('multiscale_entropy', False):
                ch_features.append(self._multiscale_entropy(ch_data))
            
            features.extend(ch_features)
        
        return np.array(features)
    
    def _extract_frequency_features(self, data: np.ndarray) -> np.ndarray:
        """
        提取频域特征（按频段分别计算）

        Args:
            data: 单个试次数据 [n_channels, n_samples]

        Returns:
            频域特征向量
        """
        features = []

        for ch in range(data.shape[0]):
            ch_data = data[ch]

            # 计算功率谱密度
            freqs, psd = welch(ch_data, fs=self.sampling_rate, nperseg=self.nperseg)

            # 🔧 修复：对每个频段分别计算特征
            for low, high in self.freq_bands:
                # 提取频段内的频率和功率
                band_mask = (freqs >= low) & (freqs <= high)
                band_psd = psd[band_mask]
                band_freqs = freqs[band_mask]

                if len(band_psd) == 0:  # 频段内无数据
                    if self.freq_features.get('power_spectral_density', False):
                        features.append(0.0)
                    if self.freq_features.get('spectral_centroid', False):
                        features.append(0.0)
                    if self.freq_features.get('spectral_bandwidth', False):
                        features.append(0.0)
                    if self.freq_features.get('spectral_rolloff', False):
                        features.append(0.0)
                    if self.freq_features.get('spectral_flatness', False):
                        features.append(0.0)
                    if self.freq_features.get('spectral_crest', False):
                        features.append(0.0)
                    if self.freq_features.get('dominant_frequency', False):
                        features.append((low + high) / 2)  # 频段中心频率
                    if self.freq_features.get('frequency_variance', False):
                        features.append(0.0)
                    continue

                # 计算频段内的特征
                if self.freq_features.get('power_spectral_density', False):
                    features.append(np.mean(band_psd))

                if self.freq_features.get('spectral_centroid', False):
                    features.append(self._spectral_centroid(band_freqs, band_psd))

                if self.freq_features.get('spectral_bandwidth', False):
                    features.append(self._spectral_bandwidth(band_freqs, band_psd))

                if self.freq_features.get('spectral_rolloff', False):
                    features.append(self._spectral_rolloff(band_freqs, band_psd))

                if self.freq_features.get('spectral_flatness', False):
                    features.append(self._spectral_flatness(band_psd))

                if self.freq_features.get('spectral_crest', False):
                    features.append(self._spectral_crest(band_psd))

                if self.freq_features.get('dominant_frequency', False):
                    features.append(band_freqs[np.argmax(band_psd)])

                if self.freq_features.get('frequency_variance', False):
                    features.append(self._frequency_variance(band_freqs, band_psd))

        return np.array(features)

    def _sample_entropy(self, data: np.ndarray, m: int = 2, r: float = 0.2) -> float:
        """计算样本熵（优化版本）"""
        try:
            N = len(data)
            if N < 50:  # 数据太短，返回简化计算
                return np.std(data) / (np.mean(np.abs(data)) + 1e-8)

            # 简化的样本熵计算
            # 使用滑动窗口方差作为复杂度度量
            window_size = min(10, N // 5)
            variances = []

            for i in range(N - window_size + 1):
                window = data[i:i + window_size]
                variances.append(np.var(window))

            # 计算方差的方差作为熵的近似
            entropy_approx = np.var(variances) / (np.mean(variances) + 1e-8)
            return min(2.0, max(0.0, entropy_approx))  # 限制范围

        except Exception:
            return 0.0

    def _approximate_entropy(self, data: np.ndarray, m: int = 2, r: float = 0.2) -> float:
        """计算近似熵（快速版本）"""
        try:
            # 使用信号的自相关函数作为近似
            if len(data) < 20:
                return 0.0

            # 计算自相关
            autocorr = np.correlate(data, data, mode='full')
            autocorr = autocorr[autocorr.size // 2:]

            # 归一化（避免除零）
            if autocorr[0] != 0:
                autocorr = autocorr / autocorr[0]
            else:
                autocorr = np.zeros_like(autocorr)

            # 计算衰减率作为熵的近似
            decay_rate = 0.0
            for i in range(1, min(10, len(autocorr))):
                if autocorr[i] < 0.1:
                    decay_rate = i / 10.0
                    break

            return max(0.0, min(2.0, 1.0 - decay_rate))

        except Exception:
            return 0.0

    def _permutation_entropy(self, data: np.ndarray, order: int = 3, delay: int = 1) -> float:
        """计算排列熵"""
        try:
            from itertools import permutations

            N = len(data)
            if N < order:
                return 0.0

            # 生成所有可能的排列
            all_perms = list(permutations(range(order)))
            perm_counts = {perm: 0 for perm in all_perms}

            # 计算排列模式
            for i in range(N - (order - 1) * delay):
                # 提取子序列
                subseq = [data[i + j * delay] for j in range(order)]
                # 获取排列模式
                sorted_indices = sorted(range(order), key=lambda x: subseq[x])
                perm = tuple(sorted_indices)
                if perm in perm_counts:
                    perm_counts[perm] += 1

            # 计算概率和熵
            total = sum(perm_counts.values())
            if total == 0:
                return 0.0

            probs = [count / total for count in perm_counts.values() if count > 0]
            pe = -sum([p * np.log2(p) for p in probs])

            return pe / np.log2(len(all_perms))  # 归一化

        except Exception:
            return 0.0

    def _fuzzy_entropy(self, data: np.ndarray, m: int = 2, r: float = 0.2) -> float:
        """计算模糊熵"""
        try:
            N = len(data)
            if N < m + 1:
                return 0.0

            # 标准化数据
            data = (data - np.mean(data)) / np.std(data)

            def _fuzzy_similarity(xi, xj, r):
                return np.exp(-np.power(np.max(np.abs(xi - xj)), 2) / r)

            def _phi(m):
                patterns = np.array([data[i:i + m] for i in range(N - m + 1)])
                phi_sum = 0.0

                for i in range(N - m + 1):
                    template_i = patterns[i]
                    similarity_sum = 0.0

                    for j in range(N - m + 1):
                        similarity_sum += _fuzzy_similarity(template_i, patterns[j], r)

                    phi_sum += np.log(similarity_sum / (N - m + 1.0))

                return phi_sum / (N - m + 1.0)

            return _phi(m) - _phi(m + 1)

        except Exception:
            return 0.0

    def _multiscale_entropy(self, data: np.ndarray, max_scale: int = 5) -> float:
        """计算多尺度熵"""
        try:
            entropies = []

            for scale in range(1, max_scale + 1):
                # 粗粒化
                if scale == 1:
                    coarse_data = data
                else:
                    N = len(data)
                    coarse_length = N // scale
                    coarse_data = np.zeros(coarse_length)

                    for i in range(coarse_length):
                        start_idx = i * scale
                        end_idx = start_idx + scale
                        coarse_data[i] = np.mean(data[start_idx:end_idx])

                # 计算样本熵
                se = self._sample_entropy(coarse_data)
                entropies.append(se)

            return np.mean(entropies)

        except Exception:
            return 0.0

    def _spectral_centroid(self, freqs: np.ndarray, psd: np.ndarray) -> float:
        """计算频谱质心"""
        try:
            return np.sum(freqs * psd) / np.sum(psd)
        except Exception:
            return 0.0

    def _spectral_bandwidth(self, freqs: np.ndarray, psd: np.ndarray) -> float:
        """计算频谱带宽"""
        try:
            centroid = self._spectral_centroid(freqs, psd)
            return np.sqrt(np.sum(((freqs - centroid) ** 2) * psd) / np.sum(psd))
        except Exception:
            return 0.0

    def _spectral_rolloff(self, freqs: np.ndarray, psd: np.ndarray, rolloff: float = 0.85) -> float:
        """计算频谱滚降"""
        try:
            total_energy = np.sum(psd)
            cumulative_energy = np.cumsum(psd)
            rolloff_idx = np.where(cumulative_energy >= rolloff * total_energy)[0]
            if len(rolloff_idx) > 0:
                return freqs[rolloff_idx[0]]
            else:
                return freqs[-1]
        except Exception:
            return 0.0

    def _spectral_flatness(self, psd: np.ndarray) -> float:
        """计算频谱平坦度"""
        try:
            # 避免log(0)
            psd_safe = psd + 1e-10
            geometric_mean = np.exp(np.mean(np.log(psd_safe)))
            arithmetic_mean = np.mean(psd)
            return geometric_mean / arithmetic_mean
        except Exception:
            return 0.0

    def _spectral_crest(self, psd: np.ndarray) -> float:
        """计算频谱峰值因子"""
        try:
            mean_psd = np.mean(psd)
            if mean_psd > 0:
                return np.max(psd) / mean_psd
            else:
                return 0.0
        except Exception:
            return 0.0

    def _frequency_variance(self, freqs: np.ndarray, psd: np.ndarray) -> float:
        """计算频率方差"""
        try:
            centroid = self._spectral_centroid(freqs, psd)
            return np.sum(((freqs - centroid) ** 2) * psd) / np.sum(psd)
        except Exception:
            return 0.0

    def get_feature_names(self) -> List[str]:
        """获取特征名称列表"""
        feature_names = []

        # 时域特征名称
        for ch in range(self.n_channels):
            for feature_name, enabled in self.time_features.items():
                if enabled:
                    feature_names.append(f"time_ch{ch+1}_{feature_name}")

        # 熵域特征名称
        for ch in range(self.n_channels):
            for feature_name, enabled in self.entropy_features.items():
                if enabled:
                    feature_names.append(f"entropy_ch{ch+1}_{feature_name}")

        # 频域特征名称
        for ch in range(self.n_channels):
            for feature_name, enabled in self.freq_features.items():
                if enabled:
                    feature_names.append(f"freq_ch{ch+1}_{feature_name}")

        return feature_names

    def get_params(self, deep: bool = True) -> dict:
        """获取参数"""
        params = super().get_params(deep)
        params.update({
            'time_features': self.time_features,
            'entropy_features': self.entropy_features,
            'freq_features': self.freq_features,
            'freq_bands': self.freq_bands,
            'nperseg': self.nperseg
        })
        return params

    def __repr__(self) -> str:
        """字符串表示"""
        return (f"TEFExtractor("
                f"time_features={sum(self.time_features.values())}, "
                f"entropy_features={sum(self.entropy_features.values())}, "
                f"freq_features={sum(self.freq_features.values())}, "
                f"total_features={self.total_features}, "
                f"fitted={self._is_fitted})")
