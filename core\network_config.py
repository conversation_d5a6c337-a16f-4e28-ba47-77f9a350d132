# -*- coding: utf-8 -*-
"""
网络配置管理
Network Configuration Management

管理API接口地址、端口配置和数据字段映射
"""

# API基础配置
API_BASE_URL = "http://*************:8082/shdekf/Api/"

# API接口端点
API_ENDPOINTS = {
    'add_patient': f"{API_BASE_URL}AppPatientServlet?act=sendPatients&data=",
    'update_patient': f"{API_BASE_URL}AppPatientServlet?act=updatePatients&data=",
    'add_treatment': f"{API_BASE_URL}AppPatientServlet?act=sendTreat&data=",
    'update_equipment': f"{API_BASE_URL}AppPatientServlet?act=updateEquipment&data="
}

# 患者数据字段映射（数据库字段 → API字段）
# 按照api_client.py中的字段定义
PATIENT_FIELD_MAPPING = {
    'bianhao': 'num',
    'name': 'name',
    'xingbie': 'sex',
    'age': 'age',
    'cardid': 'idCard',
    'bingshi': 'jiwangshi',
    'zhenduan': 'zhenduan'
}

# 治疗记录字段映射（数据库字段 → API字段）
TREATMENT_FIELD_MAPPING = {
    'shijics': 'actualTimes',
    'xiaoguo': 'commentsOfTreatment',
    'yaoqiucs': 'timesOfImagination',
    'defen': 'treatScore',
    'shijian': 'treatTime',
    'rq': 'usageTime',
    'bianh': 'patientNum',
    'zhiliaobh': 'treatNum',
    'yiyuanid': 'hospitalID',
    'keshiming': 'department',
    'shebeih': 'equipmentNum',
    'zhuzhiyis': 'attdoctor',
    'czy': 'operator',
    'shenfenzh': 'idCard'
}

# 网络请求配置
NETWORK_CONFIG = {
    'timeout': 30,  # 请求超时时间（秒）
    'retry_times': 1,  # 重试次数（设为1表示只尝试一次，不重试）
    'retry_delay': 1,  # 重试间隔（秒）
}

# 同步状态常量
SYNC_STATUS = {
    'NOT_UPLOADED': 0,  # 未上传
    'UPLOADED': 1,      # 已上传
}

# 全局变量（从配置文件获取）
# 这些值应该在应用启动时从settings.json配置文件获取
GLOBAL_CONFIG = {
    'yiyuanid_pub': 3,  # 医院ID，默认值，应从配置文件获取
    'keshi_pub': '康复科',  # 科室，默认值，应从配置文件获取
    'shebeiid_pub': '160701',  # 设备ID，默认值，应从配置文件获取
}

def get_global_config():
    """获取全局配置信息"""
    return GLOBAL_CONFIG.copy()

def update_global_config(yiyuanid=None, keshi=None, shebeiid=None):
    """更新全局配置信息"""
    if yiyuanid is not None:
        GLOBAL_CONFIG['yiyuanid_pub'] = yiyuanid
    if keshi is not None:
        GLOBAL_CONFIG['keshi_pub'] = keshi
    if shebeiid is not None:
        GLOBAL_CONFIG['shebeiid_pub'] = shebeiid
