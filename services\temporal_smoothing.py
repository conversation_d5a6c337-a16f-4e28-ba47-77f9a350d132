# -*- coding: utf-8 -*-
"""
时序平滑模块
Temporal Smoothing Module

针对低信噪比脑电信号分类结果的时序平滑处理
实现多种平滑策略以提高分类稳定性和准确性
"""

import numpy as np
import logging
from collections import deque
from typing import Dict, List, Optional, Union, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class SmoothingResult:
    """平滑结果数据类"""
    smoothed_prediction: int
    smoothed_probability: np.ndarray
    confidence: float
    smoothing_method: str
    buffer_size: int
    consistency_score: float


class TemporalSmoother:
    """时序平滑器
    
    实现多种时序平滑策略：
    1. 滑动窗口投票机制
    2. 指数加权移动平均
    3. 置信度加权平滑
    4. 自适应阈值平滑
    """
    
    def __init__(self, 
                 window_size: int = 5,
                 smoothing_method: str = "voting",
                 confidence_threshold: float = 0.6,
                 alpha: float = 0.3):
        """
        初始化时序平滑器
        
        Args:
            window_size: 滑动窗口大小
            smoothing_method: 平滑方法 ("voting", "ewma", "confidence_weighted", "adaptive")
            confidence_threshold: 置信度阈值
            alpha: 指数加权移动平均的平滑因子
        """
        self.window_size = window_size
        self.smoothing_method = smoothing_method
        self.confidence_threshold = confidence_threshold
        self.alpha = alpha
        
        # 历史数据缓冲区
        self.prediction_buffer = deque(maxlen=window_size)
        self.probability_buffer = deque(maxlen=window_size)
        self.confidence_buffer = deque(maxlen=window_size)
        
        # 指数加权移动平均状态
        self.ewma_probability = None
        self.ewma_initialized = False
        
        # 自适应阈值状态
        self.adaptive_threshold = confidence_threshold
        self.threshold_history = deque(maxlen=20)
        
        logger.info(f"时序平滑器初始化完成 - 方法: {smoothing_method}, 窗口大小: {window_size}")
    
    def update(self, prediction: int, probability: np.ndarray, confidence: float) -> SmoothingResult:
        """
        更新平滑器并返回平滑结果
        
        Args:
            prediction: 当前预测结果 (0或1)
            probability: 当前概率分布 [rest_prob, motor_imagery_prob]
            confidence: 当前置信度
            
        Returns:
            SmoothingResult: 平滑后的结果
        """
        # 添加到缓冲区
        self.prediction_buffer.append(prediction)
        self.probability_buffer.append(probability.copy())
        self.confidence_buffer.append(confidence)
        
        # 根据选择的方法进行平滑
        if self.smoothing_method == "voting":
            return self._sliding_window_voting()
        elif self.smoothing_method == "ewma":
            return self._exponential_weighted_moving_average()
        elif self.smoothing_method == "confidence_weighted":
            return self._confidence_weighted_smoothing()
        elif self.smoothing_method == "adaptive":
            return self._adaptive_threshold_smoothing()
        else:
            logger.warning(f"未知的平滑方法: {self.smoothing_method}，使用投票方法")
            return self._sliding_window_voting()
    
    def _sliding_window_voting(self) -> SmoothingResult:
        """滑动窗口投票机制"""
        if len(self.prediction_buffer) < 3:
            # 数据不足，返回当前预测
            current_pred = self.prediction_buffer[-1]
            current_prob = self.probability_buffer[-1]
            current_conf = self.confidence_buffer[-1]
            
            return SmoothingResult(
                smoothed_prediction=current_pred,
                smoothed_probability=current_prob,
                confidence=current_conf,
                smoothing_method="voting",
                buffer_size=len(self.prediction_buffer),
                consistency_score=1.0
            )
        
        # 计算投票结果
        predictions = list(self.prediction_buffer)
        vote_counts = {0: predictions.count(0), 1: predictions.count(1)}
        
        # 多数投票
        smoothed_pred = max(vote_counts, key=vote_counts.get)
        
        # 计算平均概率
        probabilities = np.array(list(self.probability_buffer))
        smoothed_prob = np.mean(probabilities, axis=0)
        
        # 计算置信度（基于一致性）
        consistency = max(vote_counts.values()) / len(predictions)
        smoothed_conf = np.max(smoothed_prob) * consistency
        
        return SmoothingResult(
            smoothed_prediction=smoothed_pred,
            smoothed_probability=smoothed_prob,
            confidence=smoothed_conf,
            smoothing_method="voting",
            buffer_size=len(self.prediction_buffer),
            consistency_score=consistency
        )
    
    def _exponential_weighted_moving_average(self) -> SmoothingResult:
        """指数加权移动平均"""
        current_prob = self.probability_buffer[-1]
        
        if not self.ewma_initialized:
            self.ewma_probability = current_prob.copy()
            self.ewma_initialized = True
        else:
            # EWMA更新
            self.ewma_probability = (self.alpha * current_prob + 
                                   (1 - self.alpha) * self.ewma_probability)
        
        # 基于平滑概率确定预测
        smoothed_pred = np.argmax(self.ewma_probability)
        smoothed_conf = np.max(self.ewma_probability)
        
        # 计算一致性分数
        recent_preds = list(self.prediction_buffer)[-3:] if len(self.prediction_buffer) >= 3 else list(self.prediction_buffer)
        if recent_preds:
            consistency = recent_preds.count(smoothed_pred) / len(recent_preds)
        else:
            consistency = 1.0
        
        return SmoothingResult(
            smoothed_prediction=smoothed_pred,
            smoothed_probability=self.ewma_probability.copy(),
            confidence=smoothed_conf,
            smoothing_method="ewma",
            buffer_size=len(self.prediction_buffer),
            consistency_score=consistency
        )
    
    def _confidence_weighted_smoothing(self) -> SmoothingResult:
        """置信度加权平滑"""
        if len(self.confidence_buffer) < 2:
            # 数据不足，返回当前预测
            return self._sliding_window_voting()
        
        # 获取历史数据
        probabilities = np.array(list(self.probability_buffer))
        confidences = np.array(list(self.confidence_buffer))
        
        # 归一化置信度作为权重
        weights = confidences / np.sum(confidences)
        
        # 加权平均概率
        smoothed_prob = np.average(probabilities, axis=0, weights=weights)
        smoothed_pred = np.argmax(smoothed_prob)
        smoothed_conf = np.max(smoothed_prob)
        
        # 计算一致性分数
        predictions = list(self.prediction_buffer)
        if predictions:
            consistency = predictions.count(smoothed_pred) / len(predictions)
        else:
            consistency = 1.0
        
        return SmoothingResult(
            smoothed_prediction=smoothed_pred,
            smoothed_probability=smoothed_prob,
            confidence=smoothed_conf,
            smoothing_method="confidence_weighted",
            buffer_size=len(self.prediction_buffer),
            consistency_score=consistency
        )
    
    def _adaptive_threshold_smoothing(self) -> SmoothingResult:
        """自适应阈值平滑"""
        # 先进行基础投票平滑
        base_result = self._sliding_window_voting()
        
        # 更新自适应阈值
        self.threshold_history.append(base_result.confidence)
        if len(self.threshold_history) >= 10:
            # 基于历史置信度调整阈值
            mean_conf = np.mean(list(self.threshold_history))
            std_conf = np.std(list(self.threshold_history))
            self.adaptive_threshold = max(0.5, min(0.8, mean_conf - 0.5 * std_conf))
        
        # 如果置信度低于自适应阈值，倾向于保持之前的预测
        if (base_result.confidence < self.adaptive_threshold and 
            len(self.prediction_buffer) >= 2):
            
            # 使用前一个预测
            prev_pred = list(self.prediction_buffer)[-2]
            
            return SmoothingResult(
                smoothed_prediction=prev_pred,
                smoothed_probability=base_result.smoothed_probability,
                confidence=base_result.confidence,
                smoothing_method="adaptive",
                buffer_size=len(self.prediction_buffer),
                consistency_score=base_result.consistency_score
            )
        
        return SmoothingResult(
            smoothed_prediction=base_result.smoothed_prediction,
            smoothed_probability=base_result.smoothed_probability,
            confidence=base_result.confidence,
            smoothing_method="adaptive",
            buffer_size=len(self.prediction_buffer),
            consistency_score=base_result.consistency_score
        )
    
    def reset(self):
        """重置平滑器状态"""
        self.prediction_buffer.clear()
        self.probability_buffer.clear()
        self.confidence_buffer.clear()
        self.ewma_probability = None
        self.ewma_initialized = False
        self.adaptive_threshold = self.confidence_threshold
        self.threshold_history.clear()
        
        logger.info("时序平滑器状态已重置")
    
    def get_status(self) -> Dict:
        """获取平滑器状态信息"""
        return {
            "window_size": self.window_size,
            "smoothing_method": self.smoothing_method,
            "buffer_size": len(self.prediction_buffer),
            "confidence_threshold": self.confidence_threshold,
            "adaptive_threshold": self.adaptive_threshold,
            "ewma_initialized": self.ewma_initialized,
            "alpha": self.alpha
        }


class MultiMethodSmoother:
    """多方法集成平滑器
    
    同时使用多种平滑方法，并根据性能动态选择最佳方法
    """
    
    def __init__(self, window_size: int = 5):
        """初始化多方法平滑器"""
        self.smoothers = {
            "voting": TemporalSmoother(window_size, "voting"),
            "ewma": TemporalSmoother(window_size, "ewma"),
            "confidence_weighted": TemporalSmoother(window_size, "confidence_weighted"),
            "adaptive": TemporalSmoother(window_size, "adaptive")
        }
        
        # 方法性能跟踪
        self.method_scores = {method: 0.0 for method in self.smoothers.keys()}
        self.method_counts = {method: 0 for method in self.smoothers.keys()}
        
        self.current_best_method = "voting"
        
        logger.info("多方法集成平滑器初始化完成")
    
    def update(self, prediction: int, probability: np.ndarray, confidence: float) -> SmoothingResult:
        """更新所有平滑器并返回最佳结果"""
        results = {}
        
        # 获取所有方法的结果
        for method, smoother in self.smoothers.items():
            results[method] = smoother.update(prediction, probability, confidence)
        
        # 选择最佳方法（基于一致性分数和置信度）
        best_method = self._select_best_method(results)
        best_result = results[best_method]
        
        # 更新方法性能
        self._update_method_performance(best_method, best_result)
        
        return best_result
    
    def _select_best_method(self, results: Dict[str, SmoothingResult]) -> str:
        """选择最佳平滑方法"""
        best_score = -1
        best_method = self.current_best_method
        
        for method, result in results.items():
            # 综合评分：一致性分数 + 置信度
            score = 0.6 * result.consistency_score + 0.4 * result.confidence
            
            if score > best_score:
                best_score = score
                best_method = method
        
        self.current_best_method = best_method
        return best_method
    
    def _update_method_performance(self, method: str, result: SmoothingResult):
        """更新方法性能统计"""
        self.method_counts[method] += 1
        
        # 使用指数移动平均更新性能分数
        alpha = 0.1
        current_score = 0.6 * result.consistency_score + 0.4 * result.confidence
        
        if self.method_counts[method] == 1:
            self.method_scores[method] = current_score
        else:
            self.method_scores[method] = (alpha * current_score + 
                                        (1 - alpha) * self.method_scores[method])
    
    def reset(self):
        """重置所有平滑器"""
        for smoother in self.smoothers.values():
            smoother.reset()
        
        self.method_scores = {method: 0.0 for method in self.smoothers.keys()}
        self.method_counts = {method: 0 for method in self.smoothers.keys()}
        self.current_best_method = "voting"
        
        logger.info("多方法集成平滑器已重置")
    
    def get_status(self) -> Dict:
        """获取集成平滑器状态"""
        return {
            "current_best_method": self.current_best_method,
            "method_scores": self.method_scores.copy(),
            "method_counts": self.method_counts.copy(),
            "individual_smoothers": {
                method: smoother.get_status() 
                for method, smoother in self.smoothers.items()
            }
        }
