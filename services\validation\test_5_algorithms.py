"""
5类算法组合性能测试脚本

使用EEG Motor Movement/Imagery Dataset的前10名受试者数据
测试5类算法组合的性能：
1. FBCSP + SVM
2. TEF + RandomForest  
3. Riemannian + MeanField
4. TangentSpace + LogisticRegression
5. PLV + SVM

确保使用与系统完全匹配的8通道数据，避免数据泄露
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import json
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from services.validation.algorithm_validation import AlgorithmValidator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('validation_5_algorithms.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class FiveAlgorithmsValidator:
    """5类算法组合验证器"""

    def __init__(self, dataset_path: str, results_dir: str = "validation_results_5_algorithms", enable_preprocessing: bool = True):
        """
        初始化5类算法验证器

        Args:
            dataset_path: EEG数据集路径
            results_dir: 结果保存目录
            enable_preprocessing: 是否启用预处理管道
        """
        self.dataset_path = dataset_path
        self.results_dir = results_dir
        self.enable_preprocessing = enable_preprocessing
        os.makedirs(results_dir, exist_ok=True)

        # 初始化算法验证器（集成预处理管道）
        self.validator = AlgorithmValidator(
            dataset_path=dataset_path,
            results_dir=results_dir,
            config_path='config/feature_extraction_optimized.json',
            enable_preprocessing=enable_preprocessing
        )
        
        # 前10名受试者ID
        self.test_subjects = [f"S{i:03d}" for i in range(1, 11)]
        
        logger.info(f"5类算法验证器初始化完成")
        logger.info(f"数据集路径: {dataset_path}")
        logger.info(f"测试受试者: {self.test_subjects}")
        logger.info(f"结果保存目录: {results_dir}")
        logger.info(f"预处理管道: {'启用' if enable_preprocessing else '禁用'}")
    
    def validate_all_subjects(self, cv_folds: int = 5) -> Dict[str, Any]:
        """
        验证所有前10名受试者的5类算法性能
        
        Args:
            cv_folds: 交叉验证折数
            
        Returns:
            完整验证结果
        """
        logger.info("开始5类算法组合性能验证")
        logger.info(f"测试受试者数量: {len(self.test_subjects)}")
        logger.info(f"交叉验证折数: {cv_folds}")
        
        # 验证数据集可用性
        available_subjects = self._check_dataset_availability()
        if not available_subjects:
            raise ValueError("没有找到可用的受试者数据")
        
        # 执行验证
        results = self.validator.validate_multiple_subjects(available_subjects, cv_folds)
        
        # 生成详细报告
        detailed_report = self._generate_detailed_report(results, available_subjects)
        
        # 保存结果
        self._save_comprehensive_results(detailed_report)
        
        logger.info("5类算法组合验证完成")
        return detailed_report
    
    def _check_dataset_availability(self) -> List[str]:
        """检查数据集中可用的受试者"""
        available_subjects = []
        
        logger.info("检查数据集可用性...")
        
        for subject_id in self.test_subjects:
            subject_path = os.path.join(self.dataset_path, subject_id)
            if os.path.exists(subject_path):
                # 检查必需的文件
                required_files = [
                    f"{subject_id}R06.edf",
                    f"{subject_id}R06.edf.event", 
                    f"{subject_id}R10.edf",
                    f"{subject_id}R10.edf.event"
                ]
                
                files_exist = all(
                    os.path.exists(os.path.join(subject_path, file)) 
                    for file in required_files
                )
                
                if files_exist:
                    available_subjects.append(subject_id)
                    logger.info(f"✓ {subject_id}: 数据文件完整")
                else:
                    logger.warning(f"✗ {subject_id}: 缺少必需文件")
            else:
                logger.warning(f"✗ {subject_id}: 目录不存在")
        
        logger.info(f"可用受试者数量: {len(available_subjects)}/{len(self.test_subjects)}")
        return available_subjects
    
    def _generate_detailed_report(self, results: Dict[str, Any], 
                                 tested_subjects: List[str]) -> Dict[str, Any]:
        """生成详细的验证报告"""
        logger.info("生成详细验证报告...")
        
        # 算法名称映射
        algorithm_names = {
            'fbcsp_svm': 'FBCSP + SVM',
            'tef_rf': 'TEF + RandomForest',
            'riemannian_meanfield': 'Riemannian + MeanField',
            'tangent_space_lr': 'TangentSpace + LogisticRegression',
            'plv_svm': 'PLV + SVM'
        }
        
        detailed_report = {
            'validation_info': {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'dataset_path': self.dataset_path,
                'tested_subjects': tested_subjects,
                'total_subjects': len(tested_subjects),
                'algorithm_count': 5,
                'algorithms': list(algorithm_names.values())
            },
            'summary_statistics': results,
            'algorithm_comparison': {},
            'performance_ranking': {},
            'recommendations': {}
        }
        
        # 算法性能对比
        if 'algorithms' in results:
            for alg_key, alg_name in algorithm_names.items():
                if alg_key in results['algorithms']:
                    alg_data = results['algorithms'][alg_key]
                    detailed_report['algorithm_comparison'][alg_name] = {
                        'mean_accuracy': alg_data.get('mean_cv_accuracy', 0),
                        'std_accuracy': alg_data.get('std_cv_accuracy', 0),
                        'subjects_tested': alg_data.get('n_subjects', 0),
                        'algorithm_key': alg_key
                    }
        
        # 性能排名
        if detailed_report['algorithm_comparison']:
            ranking = sorted(
                detailed_report['algorithm_comparison'].items(),
                key=lambda x: x[1]['mean_accuracy'],
                reverse=True
            )
            
            detailed_report['performance_ranking'] = {
                f"rank_{i+1}": {
                    'algorithm': alg_name,
                    'accuracy': f"{alg_data['mean_accuracy']:.3f}±{alg_data['std_accuracy']:.3f}",
                    'mean_accuracy': alg_data['mean_accuracy']
                }
                for i, (alg_name, alg_data) in enumerate(ranking)
            }
        
        # 生成建议
        detailed_report['recommendations'] = self._generate_recommendations(
            detailed_report['algorithm_comparison']
        )
        
        return detailed_report
    
    def _generate_recommendations(self, algorithm_comparison: Dict) -> Dict[str, str]:
        """基于验证结果生成建议"""
        if not algorithm_comparison:
            return {"error": "没有可用的算法比较数据"}
        
        # 找出最佳算法
        best_algorithm = max(
            algorithm_comparison.items(),
            key=lambda x: x[1]['mean_accuracy']
        )
        
        # 找出最稳定的算法（最小标准差）
        most_stable = min(
            algorithm_comparison.items(),
            key=lambda x: x[1]['std_accuracy']
        )
        
        recommendations = {
            'best_performance': f"{best_algorithm[0]} 表现最佳，平均准确率 {best_algorithm[1]['mean_accuracy']:.3f}",
            'most_stable': f"{most_stable[0]} 最稳定，标准差 {most_stable[1]['std_accuracy']:.3f}",
            'parameter_optimization': "建议对表现较差的算法进行参数优化",
            'ensemble_suggestion': "考虑使用表现最佳的2-3个算法构建集成模型"
        }
        
        return recommendations
    
    def _save_comprehensive_results(self, detailed_report: Dict[str, Any]):
        """保存完整的验证结果"""
        # 保存详细报告
        report_file = os.path.join(self.results_dir, "5_algorithms_detailed_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_report, f, indent=2, ensure_ascii=False)
        
        # 生成简化的CSV报告
        self._save_csv_summary(detailed_report)
        
        logger.info(f"详细报告已保存: {report_file}")
    
    def _save_csv_summary(self, detailed_report: Dict[str, Any]):
        """保存CSV格式的简化报告"""
        try:
            if 'algorithm_comparison' not in detailed_report:
                return
            
            # 准备CSV数据
            csv_data = []
            for alg_name, alg_data in detailed_report['algorithm_comparison'].items():
                csv_data.append({
                    'Algorithm': alg_name,
                    'Mean_Accuracy': alg_data['mean_accuracy'],
                    'Std_Accuracy': alg_data['std_accuracy'],
                    'Subjects_Tested': alg_data['subjects_tested']
                })
            
            # 保存CSV
            df = pd.DataFrame(csv_data)
            csv_file = os.path.join(self.results_dir, "5_algorithms_summary.csv")
            df.to_csv(csv_file, index=False)
            
            logger.info(f"CSV摘要已保存: {csv_file}")
            
        except Exception as e:
            logger.warning(f"保存CSV摘要失败: {e}")


def main():
    """主函数"""
    # 数据集路径
    dataset_path = r"D:\脑电\数据\EEG Motor MovementImagery Dataset"
    
    # 检查数据集路径
    if not os.path.exists(dataset_path):
        logger.error(f"数据集路径不存在: {dataset_path}")
        return
    
    try:
        # 创建验证器
        validator = FiveAlgorithmsValidator(dataset_path)
        
        # 执行验证
        results = validator.validate_all_subjects(cv_folds=5)
        
        # 打印简要结果
        print("\n" + "="*60)
        print("5类算法组合验证结果摘要")
        print("="*60)
        
        if 'performance_ranking' in results:
            print("\n算法性能排名:")
            for rank_key, rank_data in results['performance_ranking'].items():
                rank_num = rank_key.split('_')[1]
                print(f"{rank_num}. {rank_data['algorithm']}: {rank_data['accuracy']}")
        
        if 'recommendations' in results:
            print("\n建议:")
            for key, recommendation in results['recommendations'].items():
                print(f"- {recommendation}")
        
        print(f"\n详细结果已保存到: {validator.results_dir}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"验证过程失败: {e}")
        raise


if __name__ == "__main__":
    main()
