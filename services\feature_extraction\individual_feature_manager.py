"""
独立特征管理器

管理FBCSP、TEF、EEGNet三种特征提取器的独立训练和保存，
支持通过配置文件灵活开启/关闭各种算法，为多分类器投票系统提供支持。
"""

import numpy as np
import os
import time
import logging
from typing import Dict, List, Optional, Any

from .config import FeatureExtractionConfig, load_config
from .base_extractor import BaseFeatureExtractor

logger = logging.getLogger(__name__)

class IndividualFeatureManager:
    """
    独立特征管理器
    
    负责管理多种特征提取算法的独立使用，支持：
    - 通过配置文件灵活开启/关闭算法
    - 分别训练各种特征提取器
    - 独立保存特征数据，使用患者ID命名
    - 为多分类器投票系统提供支持
    """
    
    def __init__(self, config: Optional[FeatureExtractionConfig] = None):
        """
        初始化独立特征管理器
        
        Args:
            config: 特征提取配置，如果为None则从配置文件加载
        """
        self.config = config or load_config()
        
        # 特征提取器字典
        self._extractors: Dict[str, BaseFeatureExtractor] = {}
        
        # 状态管理
        self._is_initialized = False
        
        # 性能统计
        self._performance_stats = {
            'fit_time': 0.0,
            'save_time': 0.0,
            'n_fit_calls': 0,
            'n_save_calls': 0
        }
        
        # 初始化特征提取器
        self._initialize_extractors()
        
        logger.info(f"独立特征管理器初始化完成，启用算法: {list(self._extractors.keys())}")
    
    def _initialize_extractors(self):
        """根据配置初始化启用的特征提取器"""
        try:
            enabled_extractors = self.config.get_enabled_extractors()
            logger.info(f"正在初始化特征提取器: {enabled_extractors}")

            if 'fbcsp' in enabled_extractors:
                from .fbcsp_extractor import FBCSPExtractor
                self._extractors['fbcsp'] = FBCSPExtractor(
                    freq_bands=self.config.fbcsp.freq_bands,
                    n_components=self.config.fbcsp.n_components,
                    sampling_rate=self.config.sampling_rate,
                    reg=self.config.fbcsp.reg,
                    log=self.config.fbcsp.log
                )
                logger.info("FBCSP特征提取器已初始化")

            if 'riemannian' in enabled_extractors:
                from .riemannian_extractor import RiemannianCovarianceExtractor
                self._extractors['riemannian'] = RiemannianCovarianceExtractor(
                    metric=self.config.riemannian.metric,
                    estimator=self.config.riemannian.estimator,
                    tangent_space=self.config.riemannian.tangent_space,
                    reference_method=self.config.riemannian.reference_method,
                    regularization=self.config.riemannian.regularization
                )
                logger.info("Riemannian特征提取器已初始化")

            if 'tef' in enabled_extractors:
                from .tef_extractor import TEFExtractor
                self._extractors['tef'] = TEFExtractor(
                    sampling_rate=self.config.sampling_rate,
                    time_features=self.config.tef.time_features,
                    entropy_features=self.config.tef.entropy_features,
                    freq_features=self.config.tef.freq_features,
                    freq_bands=self.config.tef.freq_bands,
                    nperseg=self.config.tef.nperseg,
                    feature_selection=self.config.tef.feature_selection  # 🔧 修复：添加特征选择配置
                )
                logger.info("TEF特征提取器已初始化")

            if 'tangent_space' in enabled_extractors:
                from .tangent_space_extractor import TangentSpaceExtractor
                self._extractors['tangent_space'] = TangentSpaceExtractor(
                    estimator=self.config.tangent_space.estimator,
                    metric=self.config.tangent_space.metric,
                    regularization=self.config.tangent_space.regularization,
                    sampling_rate=self.config.sampling_rate,
                    n_channels=self.config.n_channels
                )
                logger.info("TangentSpace特征提取器已初始化")

            if 'plv' in enabled_extractors:
                from .plv_extractor import PLVExtractor
                self._extractors['plv'] = PLVExtractor(
                    freq_bands=self.config.plv.freq_bands,
                    channel_pairs=self.config.plv.channel_pairs,
                    filter_order=self.config.plv.filter_order,
                    sampling_rate=self.config.sampling_rate
                )
                logger.info("PLV特征提取器已初始化")

            if not self._extractors:
                raise ValueError("没有启用任何特征提取器，请检查配置文件")
                
            self._is_initialized = True
                
        except Exception as e:
            logger.error(f"特征提取器初始化失败: {e}")
            raise
    
    def get_enabled_extractors(self) -> List[str]:
        """获取启用的特征提取器列表"""
        return list(self._extractors.keys())
    
    def fit_and_save(self, X: np.ndarray, y: np.ndarray, patient_id: str,
                     version_suffix: str = None) -> Dict[str, str]:
        """
        分别训练各个特征提取器并保存特征数据

        Args:
            X: 训练数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]
            patient_id: 患者ID，用于文件命名
            version_suffix: 版本后缀，用于多轮训练（如"round_2"）

        Returns:
            保存的文件路径字典 {extractor_name: file_path}
        """
        start_time = time.time()
        saved_files = {}
        
        try:
            if not self._is_initialized:
                raise ValueError("特征管理器未初始化")
            
            logger.info(f"开始训练和保存特征，患者ID: {patient_id}, 数据形状: {X.shape}")
            
            # 创建保存目录
            from utils.path_manager import get_data_file, path_manager
            save_dir = str(get_data_file("features"))
            path_manager.ensure_directory_exists(save_dir)
            
            # 分别训练和保存每个特征提取器
            for name, extractor in self._extractors.items():
                try:
                    logger.info(f"正在训练{name}特征提取器...")
                    
                    # 训练特征提取器
                    extractor.fit(X, y)
                    
                    # 构造保存路径（支持版本后缀）
                    if version_suffix:
                        filename = f"{patient_id}_{version_suffix}_{name}.pkl"
                    else:
                        filename = f"{patient_id}_{name}.pkl"
                    filepath = os.path.join(save_dir, filename)
                    
                    # 保存特征提取器
                    extractor.save_model(filepath)
                    # 保存相对于数据目录的相对路径
                    from utils.path_manager import get_data_dir_path
                    data_dir = get_data_dir_path()
                    try:
                        relative_path = os.path.relpath(filepath, data_dir)
                        saved_files[name] = relative_path
                    except ValueError:
                        # 如果无法计算相对路径，使用绝对路径
                        saved_files[name] = filepath

                    # 创建最新版本的副本
                    latest_filename = f"{patient_id}_latest_{name}.pkl"
                    latest_filepath = os.path.join(save_dir, latest_filename)

                    try:
                        # 复制文件到latest版本（使用绝对路径）
                        with open(filepath, 'rb') as src, open(latest_filepath, 'wb') as dst:
                            dst.write(src.read())
                        logger.debug(f"创建{name}最新版本副本: {latest_filepath}")
                    except Exception as e:
                        logger.warning(f"创建{name}最新版本副本失败: {e}")

                    logger.info(f"{name}特征提取器训练完成并保存到: {filepath}")
                    
                except Exception as e:
                    logger.error(f"{name}特征提取器训练失败: {e}")
                    # 记录详细错误信息
                    import traceback
                    logger.error(f"{name}详细错误信息: {traceback.format_exc()}")
                    # 继续处理其他特征提取器，不中断整个流程
                    continue
            
            # 记录性能统计
            total_time = time.time() - start_time
            self._performance_stats['fit_time'] += total_time
            self._performance_stats['n_fit_calls'] += 1
            
            # 如果有版本后缀，创建最新版本的软链接
            if version_suffix and saved_files:
                self._create_latest_links(patient_id, saved_files, save_dir)

            logger.info(f"特征训练和保存完成，耗时: {total_time:.3f}秒，"
                       f"成功保存: {list(saved_files.keys())}")

            return saved_files
            
        except Exception as e:
            logger.error(f"特征训练和保存失败: {e}")
            raise

    def _create_latest_links(self, patient_id: str, saved_files: Dict[str, str], save_dir: str):
        """创建最新版本的软链接"""
        try:
            from utils.path_manager import get_data_dir_path
            data_dir = get_data_dir_path()

            for name, versioned_filepath in saved_files.items():
                # 创建最新版本的文件名
                latest_filename = f"{patient_id}_latest_{name}.pkl"
                latest_filepath = os.path.join(save_dir, latest_filename)

                # 删除旧的软链接（如果存在）
                if os.path.exists(latest_filepath):
                    os.remove(latest_filepath)

                # 将相对路径转换为绝对路径
                if not os.path.isabs(versioned_filepath):
                    versioned_filepath = os.path.join(data_dir, versioned_filepath)

                # 在Windows上，使用复制而不是软链接（因为权限问题）
                import shutil
                try:
                    shutil.copy2(versioned_filepath, latest_filepath)
                    logger.debug(f"创建最新版本副本: {latest_filepath}")
                except Exception as e:
                    logger.warning(f"创建最新版本副本失败: {e}")

        except Exception as e:
            logger.error(f"创建最新版本链接失败: {e}")

    def load_latest_models(self, patient_id: str) -> Dict[str, Any]:
        """加载患者的最新特征提取器模型"""
        try:
            from utils.path_manager import get_data_file
            save_dir = str(get_data_file("features"))
            loaded_models = {}

            for name in self._extractors.keys():
                # 尝试加载最新版本
                latest_filename = f"{patient_id}_latest_{name}.pkl"
                latest_filepath = os.path.join(save_dir, latest_filename)

                if os.path.exists(latest_filepath):
                    try:
                        self._extractors[name].load_model(latest_filepath)
                        loaded_models[name] = latest_filepath
                        logger.info(f"加载{name}最新模型: {latest_filepath}")
                    except Exception as e:
                        logger.error(f"加载{name}最新模型失败: {e}")
                else:
                    # 如果没有最新版本，尝试加载基础版本
                    base_filename = f"{patient_id}_{name}.pkl"
                    base_filepath = os.path.join(save_dir, base_filename)

                    if os.path.exists(base_filepath):
                        try:
                            self._extractors[name].load_model(base_filepath)
                            loaded_models[name] = base_filepath
                            logger.info(f"加载{name}基础模型: {base_filepath}")
                        except Exception as e:
                            logger.error(f"加载{name}基础模型失败: {e}")
                    else:
                        logger.warning(f"未找到{name}的模型文件")

            return loaded_models

        except Exception as e:
            logger.error(f"加载最新模型失败: {e}")
            return {}

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return self._performance_stats.copy()
    
    def validate_input(self, X: np.ndarray, y: np.ndarray) -> bool:
        """
        验证输入数据
        
        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]
            
        Returns:
            是否有效
        """
        try:
            if X.ndim != 3:
                logger.error(f"输入数据维度错误，期望3维，实际{X.ndim}维")
                return False
            
            if X.shape[1] != self.config.n_channels:
                logger.error(f"通道数错误，期望{self.config.n_channels}，实际{X.shape[1]}")
                return False
            
            if X.shape[2] != self.config.n_samples:
                logger.error(f"样本数错误，期望{self.config.n_samples}，实际{X.shape[2]}")
                return False
            
            if len(y) != X.shape[0]:
                logger.error(f"标签数量与试次数不匹配，数据{X.shape[0]}，标签{len(y)}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"输入验证失败: {e}")
            return False
