{"fbcsp_svm": {"type": "SVC", "params": {"C": 0.1, "kernel": "linear", "probability": true, "random_state": 42, "class_weight": "balanced"}, "_comment": "使用更强的正则化，避免小样本过拟合"}, "tef_rf": {"type": "RandomForestClassifier", "params": {"n_estimators": 20, "max_depth": 4, "min_samples_split": 10, "min_samples_leaf": 5, "max_features": 0.5, "bootstrap": true, "oob_score": true, "random_state": 42, "class_weight": "balanced"}, "_comment": "大幅简化参数防止过拟合：树数量50→10，深度8→3，提高样本要求5→20/2→10"}, "riemannian_meanfield": {"type": "MeanField", "params": {"power_list": [-0.5, 0, 0.5], "method_label": "sum_means", "metric": "logeuclid", "n_jobs": 1}, "fallback": {"type": "SVC", "params": {"C": 0.1, "kernel": "rbf", "gamma": "scale", "probability": true, "random_state": 42}}}, "tangent_space_lr": {"type": "LogisticRegression", "params": {"C": 1, "max_iter": 10000, "random_state": 42, "solver": "lbfgs", "class_weight": "balanced", "penalty": "l2"}, "_comment": "增强L1正则化：C从1.0→0.1，改用liblinear求解器，移除elasticnet"}, "plv_svm": {"type": "SVC", "params": {"C": 0.1, "kernel": "linear", "gamma": "auto", "probability": true, "random_state": 42, "class_weight": "balanced"}, "_comment": "PLV特征专用SVM分类器，RBF核适合低SNR环境的非线性模式"}, "dynamic_weights": {"enabled": true, "window_size": 20, "update_frequency": 10, "temperature": 2.0, "min_weight": 0.05, "performance_decay": 0.95, "_comment": "动态权重配置，基于2024年最新研究，提高集成稳定性"}, "classifier_smoothing": {"enabled": true, "fbcsp_svm": {"enabled": true, "alpha": 0.3}, "riemannian_meanfield": {"enabled": false, "alpha": 0.2}, "tangent_space_lr": {"enabled": false, "alpha": 0.2}, "plv_svm": {"enabled": false, "alpha": 0.1}, "tef_rf": {"enabled": true, "alpha": 0.25}, "_comment": "分类器输出平滑配置，基于EEG BCI研究，减少FBCSP等算法的极端输出"}}