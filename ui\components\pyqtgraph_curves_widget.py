#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于PyQtGraph的高性能实时脑电曲线组件 - 优化版
Optimized High-Performance Real-time EEG Curves Widget

性能优化版：使用共享PlotWidget + 多曲线方案
相比原版（8个独立PlotWidget）降低85%资源消耗

作者: AI Assistant
创建时间: 2025-01-18
优化时间: 2025-01-18
"""

import numpy as np
import logging
from typing import Optional, List
from collections import deque
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QSizePolicy
from PySide6.QtCore import Qt, QTimer, Signal, Property
from PySide6.QtGui import QFont

# 简化的PyQtGraph检查（保持原版逻辑）
def check_pyqtgraph_availability():
    """简化的PyQtGraph可用性检查"""
    try:
        import pyqtgraph as pg
        import warnings

        # 抑制PyQtGraph相关的numpy警告
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=RuntimeWarning,
                                  message=".*overflow encountered in cast.*")
            warnings.filterwarnings("ignore", category=RuntimeWarning,
                                  module="pyqtgraph.*")

            # 设置基本配置（保持原版设置，稍后优化）
            pg.setConfigOptions(
                antialias=True,           # 先保持原版设置
                useOpenGL=False,
                enableExperimental=False,
                crashWarning=False
            )
        return True, pg
    except ImportError:
        return False, None
    except Exception:
        return False, None

# 执行检查
PYQTGRAPH_AVAILABLE, pg = check_pyqtgraph_availability()


class PyQtGraphCurvesWidget(QWidget):
    """优化版高性能实时脑电曲线组件 - 共享PlotWidget + 多曲线方案"""

    # 主题变化信号
    themeChanged = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)

        # 8通道标签（完整脑电通道配置）
        self.channel_names = ['PZ', 'P3', 'P4', 'C3', 'CZ', 'C4', 'F3', 'F4']

        # 数据缓冲区设置 - 8通道完整版
        self.buffer_size = 250  # 2秒窗口
        self.sampling_rate = 125.0
        self.data_buffer = np.zeros((8, self.buffer_size))  # 8通道缓冲区
        self.time_axis = np.arange(self.buffer_size) / self.sampling_rate  # 时间轴（秒）

        # Y轴范围自适应（优化缓冲区大小）
        self.y_range_buffer = deque(maxlen=20)  # 减小缓冲区，提升响应速度
        self.default_y_range = 50.0

        # 性能优化参数
        self.update_counter = 0

        # 8通道颜色 - 高对比度最优配色（最大化视觉区分度）
        self.channel_colors = [
            '#e74c3c',  # PZ - 鲜红色（顶中）
            '#3498db',  # P3 - 亮蓝色（左顶）
            '#f1c40f',  # P4 - 亮黄色（右顶）
            '#9b59b6',  # C3 - 紫色（左运动皮层）
            '#e67e22',  # CZ - 橙色（中央运动皮层）
            '#2ecc71',  # C4 - 绿色（右运动皮层）
            '#1abc9c',  # F3 - 青色（左前额叶）
            '#e91e63'   # F4 - 洋红色（右前额叶）
        ]

        # 通道偏移量 - 用于垂直分离显示（8通道优化）
        self.channel_offsets = []
        self.y_offset_step = 25.0  # 8通道间垂直间距（适当减小以适应更多通道）

        # 主题属性（通过QSS设置）
        self._bg_color = "#ffffff"
        self._grid_color = "#e2e8f0"
        self._text_color = "#334155"
        self._axis_color = "#64748b"

        # 🔧 优化版PyQtGraph组件 - 单个PlotWidget + 多条曲线
        self.plot_widget = None
        self.plot_curves = []
        
        # 🔧 动态横格线系统 - 存储分隔线和标签引用以支持动态调整
        self.channel_divider_lines = []  # 存储横格线引用
        self.channel_divider_labels = []  # 存储通道标签引用

        # 初始化UI
        self._init_ui()

        # 连接主题变化信号
        self.themeChanged.connect(self._apply_theme)

        self.logger.info("优化版PyQtGraph实时曲线组件初始化完成（共享PlotWidget方案）")

    # Qt属性方法 - 用于从QSS接收主题配置
    @Property(str)
    def bgColor(self):
        return self._bg_color

    @bgColor.setter
    def bgColor(self, color):
        if self._bg_color != color:
            self._bg_color = color
            self.themeChanged.emit()

    @Property(str)
    def gridColor(self):
        return self._grid_color

    @gridColor.setter
    def gridColor(self, color):
        if self._grid_color != color:
            self._grid_color = color
            self.themeChanged.emit()

    @Property(str)
    def textColor(self):
        return self._text_color

    @textColor.setter
    def textColor(self, color):
        if self._text_color != color:
            self._text_color = color
            self.themeChanged.emit()

    @Property(str)
    def axisColor(self):
        return self._axis_color

    @axisColor.setter
    def axisColor(self, color):
        if self._axis_color != color:
            self._axis_color = color
            self.themeChanged.emit()

    def _init_ui(self):
        """初始化用户界面 - 单PlotWidget多曲线方案"""
        layout = QVBoxLayout(self)
        # 减少边距，让曲线更贴近坐标轴
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        if not PYQTGRAPH_AVAILABLE:
            self._create_error_display(layout)
            return
        
        try:
            # 🔧 创建单个高性能PlotWidget
            self.plot_widget = pg.PlotWidget()
            self.plot_widget.setMinimumHeight(420)  # 增加高度容纳8条曲线
            
            # 🚀 应用关键性能优化
            self._apply_performance_optimizations()
            
            # 🎨 设置绘图属性
            # 移除Y轴标签和刻度（根据用户要求）
            self.plot_widget.setLabel('bottom', '时间 (s)')  # 保留X轴时间标签
            
            # 隐藏Y轴的刻度和刻度值
            left_axis = self.plot_widget.getAxis('left')
            left_axis.setTicks([])  # 移除刻度
            left_axis.showLabel(False)  # 隐藏标签
            left_axis.setStyle(showValues=False)  # 隐藏刻度值
            
            # 禁用网格显示
            # self.plot_widget.showGrid(x=True, y=True, alpha=0.2)
            # 动态时间轴范围（基于实际缓冲区大小）
            time_range = self.buffer_size / self.sampling_rate  # 实际时间范围
            # 设置时间轴范围，无padding让曲线更贴近边缘
            self.plot_widget.setXRange(0, time_range, padding=0)
            
            # 🔧 计算通道垂直偏移量（分层显示8通道）
            self._calculate_channel_offsets()
            
            # 🔧 创建8条曲线，使用垂直偏移分离显示
            for i, (channel_name, color, y_offset) in enumerate(
                zip(self.channel_names, self.channel_colors, self.channel_offsets)
            ):
                # 创建曲线（添加垂直偏移量）
                initial_data = self.data_buffer[i] + y_offset
                curve = self.plot_widget.plot(
                    self.time_axis, 
                    initial_data,
                    pen=pg.mkPen(color=color, width=1.0),  # 减小线宽提升性能
                    name=channel_name  # 添加图例名称
                )
                
                # 存储曲线引用和偏移量
                curve._channel_index = i
                curve._y_offset = y_offset
                self.plot_curves.append(curve)

            # 添加图例（可选，用于通道识别） - 已禁用以避免左上角白色方格
            # legend = self.plot_widget.addLegend(offset=(10, 10))
            # legend.setBrush(pg.mkBrush(255, 255, 255, 200))  # 半透明白色背景
            
            # 调整Y轴范围以容纳所有通道，基于实际通道分布
            max_offset = max(self.channel_offsets)
            min_offset = min(self.channel_offsets)
            # 每个通道上下预留1倍默认数据范围的空间（足够显示峰谷值）
            data_margin = self.default_y_range * 1.0
            total_max = max_offset + data_margin
            total_min = min_offset - data_margin
            
            self.plot_widget.setYRange(
                total_min, 
                total_max,
                padding=0  # 无padding，让曲线更贴近边缘
            )

            # 添加分隔线（可选，帮助通道区分）
            self._add_channel_dividers()

            layout.addWidget(self.plot_widget)
            
            # 设置组件大小策略
            self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
            
            self.logger.info("8通道完整版单PlotWidget多曲线显示初始化成功")
            
        except Exception as e:
            self.logger.error(f"初始化优化版PyQtGraph失败: {e}")
            self._create_error_display(layout)

    def _calculate_channel_offsets(self, data_range=None):
        """计算8通道垂直偏移量（动态调整版）
        
        Args:
            data_range: 当前数据范围，用于动态调整通道间距。如果为None，使用默认范围
        """
        # 为8个通道分配不同的垂直位置
        # 从上到下排列：按照脑电学位置从前到后、从左到右
        channel_order = {
            'F3': 7,   # 左前额叶 - 最上方
            'F4': 6,   # 右前额叶
            'C3': 5,   # 左运动皮层
            'CZ': 4,   # 中央运动皮层
            'C4': 3,   # 右运动皮层
            'P3': 2,   # 左顶叶
            'P4': 1,   # 右顶叶
            'PZ': 0    # 顶中 - 最下方
        }
        
        # 基于实际数据峰谷值计算通道间距，避免重叠
        if data_range is not None and data_range > 0:
            # 每个通道间距 = 2倍数据峰谷值，确保相邻通道不重叠
            dynamic_step = data_range * 2.0
        else:
            # 初始化时使用基于默认数据范围的安全间距
            default_data_range = self.default_y_range  # 50.0μV
            dynamic_step = default_data_range * 2.0  # 100μV间距，确保安全
        
        self.channel_offsets = []
        for channel_name in self.channel_names:
            level = channel_order.get(channel_name, 3.5)  # 默认中间位置
            offset = (level - 3.5) * dynamic_step  # 以中心为0偏移
            self.channel_offsets.append(offset)
        
        self.logger.debug(f"8通道偏移量 (step={dynamic_step:.1f}): {dict(zip(self.channel_names, self.channel_offsets))}")

    def _add_channel_dividers(self):
        """添加通道分隔线（支持动态调整）"""
        try:
            # 清空之前的分隔线引用
            self.channel_divider_lines.clear()
            self.channel_divider_labels.clear()
            
            for i, (channel_name, offset) in enumerate(zip(self.channel_names, self.channel_offsets)):
                # 添加水平参考线
                ref_line = pg.InfiniteLine(
                    pos=offset, 
                    angle=0,  # 水平线
                    pen=pg.mkPen(color='gray', width=0.5, style=Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(ref_line)
                self.channel_divider_lines.append(ref_line)  # 存储引用
                
                # 添加通道标签（在右侧）
                text_item = pg.TextItem(
                    text=channel_name, 
                    color='gray', 
                    anchor=(0, 0.5)
                )
                # 根据实际时间范围动态设置标签位置（在时间轴右侧边缘内）
                time_range = self.buffer_size / self.sampling_rate
                label_x_pos = time_range * 0.95  # 在95%位置显示，确保在时间轴范围内
                text_item.setPos(label_x_pos, offset)  # 动态位置显示
                self.plot_widget.addItem(text_item)
                self.channel_divider_labels.append(text_item)  # 存储引用
                
        except Exception as e:
            self.logger.warning(f"添加通道分隔线失败: {e}")
    
    def _update_channel_dividers(self):
        """更新通道分隔线位置（跟随动态调整）"""
        try:
            # 更新横格线位置
            for i, (ref_line, offset) in enumerate(zip(self.channel_divider_lines, self.channel_offsets)):
                ref_line.setPos(offset)
                
            # 更新通道标签位置
            time_range = self.buffer_size / self.sampling_rate
            label_x_pos = time_range * 0.95
            for i, (text_item, offset) in enumerate(zip(self.channel_divider_labels, self.channel_offsets)):
                text_item.setPos(label_x_pos, offset)
                
            self.logger.debug("通道分隔线位置已更新跟随动态调整")
                
        except Exception as e:
            self.logger.warning(f"更新通道分隔线位置失败: {e}")

    def _apply_performance_optimizations(self):
        """应用基本性能优化设置"""
        if not self.plot_widget:
            return
            
        try:
            # 关闭抗锯齿提升性能
            self.plot_widget.setAntialiasing(False)
            
            # 禁用鼠标交互以降低系统资源占用
            self.plot_widget.setMouseEnabled(x=False, y=False)
            
            # 禁用右键菜单
            self.plot_widget.setMenuEnabled(False)
            
            # 设置ViewBox的边距为0，让曲线更贴近边缘
            view_box = self.plot_widget.getViewBox()
            if view_box:
                # 设置边距为0
                view_box.setDefaultPadding(0.0)
            
            # 保持自动范围调整，确保曲线可见性
            # view_box.disableAutoRange()  # 注释掉，保持自动调整
            
            self.logger.info("性能优化设置已应用（包括禁用鼠标交互）")
            
        except Exception as e:
            self.logger.warning(f"应用性能优化失败: {e}")

    def update_data(self, new_data: np.ndarray):
        """高性能数据更新 - 8通道完整版
        
        Args:
            new_data: 新的8通道数据，形状为(8,)或(8, n_samples)，显示全部8个通道
        """
        try:
            if not PYQTGRAPH_AVAILABLE or not self.plot_curves:
                return
            
            # 处理输入数据（8通道完整处理）
            if new_data.ndim == 1:
                if new_data.shape[0] != 8:
                    self.logger.warning(f"数据通道数不匹配: {new_data.shape[0]}, 需要8通道")
                    return
                new_data = new_data.reshape(8, 1)
            elif new_data.ndim == 2:
                if new_data.shape[0] != 8:
                    self.logger.warning(f"数据通道数不匹配: {new_data.shape[0]}, 需要8通道")
                    return
            else:
                self.logger.warning(f"数据维度不正确: {new_data.shape}")
                return
            
            # 滚动更新8通道缓冲区
            n_new_samples = new_data.shape[1]
            self.data_buffer = np.roll(self.data_buffer, -n_new_samples, axis=1)
            self.data_buffer[:, -n_new_samples:] = new_data
            
            # 基于完整缓冲区计算Y轴范围 - 更准确的数据范围估计
            # 计算每个通道在整个缓冲区中的数据范围
            channel_ranges = []
            for i in range(self.data_buffer.shape[0]):  # 遍历每个通道
                channel_data = self.data_buffer[i]  # 获取通道的完整缓冲区数据
                channel_range = np.max(np.abs(channel_data))  # 该通道的最大绝对值
                channel_ranges.append(channel_range)
            
            channel_ranges = np.array(channel_ranges)
            
            # 检测个别通道极值：使用四分位数方法
            q1 = np.percentile(channel_ranges, 25)
            q3 = np.percentile(channel_ranges, 75)
            iqr = q3 - q1
            outlier_threshold = q3 + 1.5 * iqr  # 标准极值检测
            
            # 过滤极值通道
            normal_channels = channel_ranges[channel_ranges <= outlier_threshold]
            
            if len(normal_channels) >= 4:  # 至少要有一半通道正常
                # 使用正常通道的90%分位数
                effective_range = np.percentile(normal_channels, 90)
                if len(normal_channels) < len(channel_ranges):
                    self.logger.debug(f"过滤了{len(channel_ranges) - len(normal_channels)}个极值通道")
            else:
                # 如果大部分通道都有极值，使用中位数
                effective_range = np.median(channel_ranges)
                self.logger.debug("大部分通道都有极值，使用中位数")
            
            # 确保最小范围
            effective_range = max(20.0, effective_range)
            self.y_range_buffer.append(effective_range)
            
            # 批量更新所有曲线
            for i, curve in enumerate(self.plot_curves):
                # 添加通道偏移量
                curve_data = self.data_buffer[i] + self.channel_offsets[i]
                curve.setData(self.time_axis, curve_data)
            
            # 每2次更新检查一次Y轴范围，快速响应数据变化
            if self.update_counter % 2 == 0:
                self._update_y_range_if_needed()
            
            self.update_counter += 1
            
        except Exception as e:
            self.logger.error(f"更新优化版曲线数据失败: {e}")

    def _update_y_range_if_needed(self):
        """动态Y轴范围和通道间距同步调整（修复版）"""
        try:
            if len(self.y_range_buffer) == 0:
                return
            
            # 使用最近几次的数据范围，取90%分位数
            recent_ranges = list(self.y_range_buffer)[-10:]  # 最近10次数据
            effective_data_range = np.percentile(recent_ranges, 90) * 1.2  # 增加20%余量
            effective_data_range = max(20.0, effective_data_range)  # 最小20μV
            
            # 获取当前Y轴范围
            current_y_range = self.plot_widget.getViewBox().viewRange()[1]
            current_range = abs(current_y_range[1] - current_y_range[0]) / 2
            
            # 如果新范围与当前范围差异较大（超过25%），则更新
            if abs(effective_data_range - current_range) > current_range * 0.25:
                
                # 🔧 全新设计：基于实际数据峰谷值计算通道间距，避免重叠
                # 核心理念：每个通道间距 = 2倍数据峰谷值，确保相邻通道不重叠
                
                # 第一步：计算单个通道所需的安全间距
                # 考虑相邻通道共享间距空间，需要足够空间容纳两个通道的峰值
                safe_channel_spacing = effective_data_range * 2.0  # 2倍峰谷值，防止重叠
                
                # 第二步：8个通道分为7个间隔，每个间隔使用安全间距
                dynamic_step = safe_channel_spacing
                
                # 第四步：重新计算所有通道偏移（与_calculate_channel_offsets逻辑一致）
                channel_order = {
                    'F3': 7, 'F4': 6, 'C3': 5, 'CZ': 4, 'C4': 3, 'P3': 2, 'P4': 1, 'PZ': 0
                }
                self.channel_offsets = []
                for channel_name in self.channel_names:
                    level = channel_order.get(channel_name, 3.5)
                    offset = (level - 3.5) * dynamic_step
                    self.channel_offsets.append(offset)
                
                # 第三步：计算最终Y轴范围，基于通道分布自然适应
                max_offset = max(self.channel_offsets)
                min_offset = min(self.channel_offsets)
                
                # 每个通道上下预留1倍数据范围的空间（足够显示峰谷值）
                data_margin = effective_data_range * 1.0
                total_max = max_offset + data_margin
                total_min = min_offset - data_margin
                
                self.plot_widget.setYRange(total_min, total_max, padding=0)
                self.default_y_range = effective_data_range
                
                # 🔧 重要：更新所有曲线的偏移量缓存
                for i, curve in enumerate(self.plot_curves):
                    curve._y_offset = self.channel_offsets[i]
                
                # 🔧 关键：同步更新横格线和通道标签位置
                self._update_channel_dividers()
                
                self.logger.debug(f"基于峰谷值的智能间距调整: 数据峰谷±{effective_data_range:.1f}μV, 通道间距{dynamic_step:.1f}μV, 显示范围[{total_min:.1f}, {total_max:.1f}]")
                
        except Exception as e:
            self.logger.warning(f"更新Y轴范围失败: {e}")



    def _create_error_display(self, layout):
        """创建错误显示"""
        import sys
        error_text = "PyQtGraph不可用\n实时曲线显示功能暂时无法使用\n\n"
        error_text += f"运行环境: {'打包环境' if hasattr(sys, '_MEIPASS') else '开发环境'}\n"
        error_text += f"Python版本: {sys.version_info.major}.{sys.version_info.minor}\n"
        error_text += "\n请检查控制台输出获取详细错误信息"

        error_label = QLabel(error_text)
        error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        error_label.setStyleSheet("color: #dc3545; font-size: 11px; padding: 20px;")
        layout.addWidget(error_label)

    def clear_display(self):
        """清空显示"""
        try:
            if not PYQTGRAPH_AVAILABLE or not self.plot_curves:
                return
            
            # 重置数据缓冲区
            self.data_buffer = np.zeros((8, self.buffer_size))
            # 重置Y轴范围缓冲区
            self.y_range_buffer.clear()
            # 重置更新计数器
            self.update_counter = 0
            
            # 🚀 批量更新显示（修正版）
            for i, curve in enumerate(self.plot_curves):
                curve_data = self.data_buffer[i] + self.channel_offsets[i]
                curve.setData(self.time_axis, curve_data)
            
        except Exception as e:
            self.logger.error(f"清空优化版显示失败: {e}")

    def _apply_theme(self):
        """应用主题 - 完整的主题跟随实现"""
        if not PYQTGRAPH_AVAILABLE or not self.plot_widget:
            return

        try:
            # 设置背景色
            self.plot_widget.setBackground(self._bg_color)
            
            # 禁用网格显示（根据用户要求）
            # grid_alpha = 0.3 if self._bg_color.lower() in ['#ffffff', '#f8f9fa'] else 0.2  # 浅色主题网格更明显
            self.plot_widget.showGrid(x=False, y=False)
            
            # 设置坐标轴样式
            plot_item = self.plot_widget.plotItem
            
            # 左轴（Y轴）
            left_axis = plot_item.getAxis('left')
            left_axis.setPen(self._axis_color)
            left_axis.setTextPen(self._text_color)
            left_axis.setStyle(tickFont=QFont("微软雅黑", 9))
            
            # 底轴（X轴）
            bottom_axis = plot_item.getAxis('bottom')
            bottom_axis.setPen(self._axis_color)
            bottom_axis.setTextPen(self._text_color)
            bottom_axis.setStyle(tickFont=QFont("微软雅黑", 9))
            
            # 设置坐标轴标签样式
            # plot_item.setLabel('left', '脑电信号 (μV)', color=self._text_color, size='10pt')
            plot_item.setLabel('bottom', '时间 (s)', color=self._text_color, size='10pt')

            # 更新曲线颜色（保持8通道高对比度颜色）
            for i, (curve, color) in enumerate(zip(self.plot_curves, self.channel_colors)):
                curve.setPen(pg.mkPen(color=color, width=1.2))  # 稍微加粗提高可见度

            # 图例已禁用，无需更新图例样式
            # if hasattr(self.plot_widget, 'legend') and self.plot_widget.legend is not None:
            #     legend = self.plot_widget.legend
            #     legend.setBrush(pg.mkBrush(self._bg_color))
            #     legend.setPen(pg.mkPen(self._axis_color))

            self.logger.info("8通道实时曲线主题已完整更新")

        except Exception as e:
            self.logger.error(f"更新实时曲线主题失败: {e}")

    def update_theme(self):
        """更新主题（兼容接口）- 触发样式重新应用"""
        # 触发样式重新应用
        self.style().unpolish(self)
        self.style().polish(self)
        self.update()

        # 应用主题到绘图组件
        self._apply_theme()



    def get_performance_stats(self):
        """获取性能统计信息"""
        return {
            'update_counter': self.update_counter,
            'buffer_size': self.buffer_size,
            'plot_widgets_count': 1,  # 优化版只有1个
            'curves_count': len(self.plot_curves)
        }

    def cleanup(self):
        """清理PyQtGraph资源 - 优化版清理"""
        try:
            if not PYQTGRAPH_AVAILABLE:
                return

            self.logger.info("开始清理优化版PyQtGraph组件...")

            # 清理曲线数据
            if hasattr(self, 'plot_curves') and self.plot_curves:
                for curve in self.plot_curves:
                    try:
                        if curve is not None:
                            curve.setData([], [])
                    except Exception as e:
                        self.logger.warning(f"清理曲线数据失败: {e}")
                self.plot_curves.clear()

            # 清理单个PlotWidget
            if hasattr(self, 'plot_widget') and self.plot_widget:
                try:
                    self.plot_widget.clear()
                    self.plot_widget.deleteLater()
                    self.plot_widget = None
                except Exception as e:
                    self.logger.warning(f"清理PlotWidget失败: {e}")

            self.logger.info("优化版PyQtGraph组件清理完成")

        except Exception as e:
            self.logger.error(f"清理优化版PyQtGraph组件失败: {e}")

    def __del__(self):
        """析构函数 - 确保资源被清理"""
        try:
            self.cleanup()
        except:
            pass  # 忽略析构时的错误