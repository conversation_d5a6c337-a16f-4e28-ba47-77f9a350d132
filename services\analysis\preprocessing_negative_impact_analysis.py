"""
预处理负面影响深度分析

分析为什么预处理管道对PhysioNet EEG Motor Movement/Imagery Dataset
产生负面影响的根本原因
"""

import numpy as np
import matplotlib.pyplot as plt
import json
from datetime import datetime

def analyze_preprocessing_negative_impact():
    """分析预处理负面影响的根本原因"""
    
    analysis_lines = []
    
    # 报告头部
    analysis_lines.append("预处理负面影响深度分析报告")
    analysis_lines.append("=" * 60)
    analysis_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    analysis_lines.append("分析目标: 理解预处理管道对PhysioNet数据集的负面影响")
    analysis_lines.append("")
    
    # 核心发现
    analysis_lines.append("🔍 核心发现")
    analysis_lines.append("=" * 30)
    analysis_lines.append("预处理管道对所有5类算法都产生了负面影响：")
    analysis_lines.append("• 平均性能下降: 8.1%")
    analysis_lines.append("• 受影响算法: 5/5 (100%)")
    analysis_lines.append("• 显著下降算法: 3/5 (TEF, Riemannian, TangentSpace)")
    analysis_lines.append("• 轻微下降算法: 2/5 (FBCSP, PLV)")
    analysis_lines.append("")
    
    # 可能原因分析
    analysis_lines.append("🧐 可能原因分析")
    analysis_lines.append("=" * 30)
    
    # 原因1: 数据集特性
    analysis_lines.append("1️⃣ PhysioNet数据集特性问题")
    analysis_lines.append("-" * 40)
    analysis_lines.append("• 数据已经过预处理: PhysioNet数据可能已经过基础预处理")
    analysis_lines.append("• 采样率: 160Hz，相对较低，过度滤波可能损失信息")
    analysis_lines.append("• 记录环境: 实验室环境，噪声相对较少")
    analysis_lines.append("• 电极数量: 64通道，但我们只用8通道，信息已经损失")
    analysis_lines.append("")
    
    # 原因2: 滤波器设计问题
    analysis_lines.append("2️⃣ 滤波器设计不当")
    analysis_lines.append("-" * 40)
    analysis_lines.append("• 8-30Hz带通滤波: 可能过于严格，损失有用信息")
    analysis_lines.append("  - 移除了4-8Hz: 可能包含重要的慢波成分")
    analysis_lines.append("  - 移除了30-40Hz: 可能包含有用的高频特征")
    analysis_lines.append("• 50Hz陷波滤波: 在160Hz采样率下可能引入伪影")
    analysis_lines.append("• 滤波器阶数: 6阶可能过高，引入相位失真")
    analysis_lines.append("")
    
    # 原因3: RLS自适应滤波问题
    analysis_lines.append("3️⃣ RLS自适应滤波器问题")
    analysis_lines.append("-" * 40)
    analysis_lines.append("• 过度自适应: 可能移除了有用的信号成分")
    analysis_lines.append("• 参数设置: λ=0.99, δ=1.0可能不适合这个数据集")
    analysis_lines.append("• 算法假设: RLS假设信号是平稳的，但EEG是非平稳的")
    analysis_lines.append("• 计算复杂度: 可能引入数值误差")
    analysis_lines.append("")
    
    # 原因4: 标准化问题
    analysis_lines.append("4️⃣ Z-score标准化问题")
    analysis_lines.append("-" * 40)
    analysis_lines.append("• 时间窗口标准化: 可能破坏了时间序列的相对关系")
    analysis_lines.append("• 通道间标准化: 可能破坏了通道间的相对幅度关系")
    analysis_lines.append("• 分布假设: 假设数据是正态分布，但EEG可能不是")
    analysis_lines.append("• 动态范围: 可能压缩了重要的幅度信息")
    analysis_lines.append("")
    
    # 原因5: 算法特异性
    analysis_lines.append("5️⃣ 算法特异性影响")
    analysis_lines.append("-" * 40)
    analysis_lines.append("• TEF算法: 时域特征对预处理非常敏感")
    analysis_lines.append("  - 统计特征(均值、方差)被标准化破坏")
    analysis_lines.append("  - 频域特征被滤波器改变")
    analysis_lines.append("• Riemannian算法: 协方差矩阵对信号质量敏感")
    analysis_lines.append("  - 过度滤波可能改变协方差结构")
    analysis_lines.append("  - 标准化可能破坏矩阵的几何性质")
    analysis_lines.append("• TangentSpace算法: 切空间映射对数据分布敏感")
    analysis_lines.append("  - 预处理改变了数据的流形结构")
    analysis_lines.append("")
    
    # 原因6: 数据泄露问题
    analysis_lines.append("6️⃣ 潜在的数据泄露")
    analysis_lines.append("-" * 40)
    analysis_lines.append("• 全局标准化: 如果使用全局统计量可能造成泄露")
    analysis_lines.append("• 滤波器状态: 滤波器的初始状态可能包含未来信息")
    analysis_lines.append("• RLS参数: 自适应参数可能包含测试集信息")
    analysis_lines.append("")
    
    # 文献证据
    analysis_lines.append("📚 文献证据支持")
    analysis_lines.append("=" * 30)
    analysis_lines.append("根据搜索到的文献:")
    analysis_lines.append("• 多篇论文使用0.5-100Hz带通滤波器")
    analysis_lines.append("• PhysioNet数据集通常只需要基础预处理")
    analysis_lines.append("• 过度预处理可能损害BCI性能")
    analysis_lines.append("• 不同算法对预处理的敏感性不同")
    analysis_lines.append("")
    
    # 对比分析
    analysis_lines.append("🔄 3人vs10人测试对比")
    analysis_lines.append("=" * 30)
    analysis_lines.append("• 3人测试: 预处理轻微正面影响")
    analysis_lines.append("  - 可能是统计噪声或个体差异")
    analysis_lines.append("  - 样本量太小，结果不可靠")
    analysis_lines.append("• 10人测试: 预处理显著负面影响")
    analysis_lines.append("  - 更大样本量揭示了真实效应")
    analysis_lines.append("  - 统计显著性更可靠")
    analysis_lines.append("")
    
    # 技术建议
    analysis_lines.append("🛠️ 技术改进建议")
    analysis_lines.append("=" * 30)
    analysis_lines.append("立即改进:")
    analysis_lines.append("1. 停用当前预处理管道")
    analysis_lines.append("2. 使用更宽的带通滤波器 (1-50Hz)")
    analysis_lines.append("3. 移除RLS自适应滤波器")
    analysis_lines.append("4. 使用更温和的标准化方法")
    analysis_lines.append("")
    analysis_lines.append("长期优化:")
    analysis_lines.append("1. 针对每种算法设计专门的预处理")
    analysis_lines.append("2. 使用数据驱动的预处理参数选择")
    analysis_lines.append("3. 实施交叉验证的预处理参数优化")
    analysis_lines.append("4. 考虑使用深度学习端到端方法")
    analysis_lines.append("")
    
    # 实验验证建议
    analysis_lines.append("🧪 实验验证建议")
    analysis_lines.append("=" * 30)
    analysis_lines.append("1. 逐步移除预处理组件测试:")
    analysis_lines.append("   • 只用带通滤波器")
    analysis_lines.append("   • 只用陷波滤波器")
    analysis_lines.append("   • 只用标准化")
    analysis_lines.append("2. 参数敏感性分析:")
    analysis_lines.append("   • 不同带通滤波器频段")
    analysis_lines.append("   • 不同RLS参数")
    analysis_lines.append("   • 不同标准化方法")
    analysis_lines.append("3. 算法特异性分析:")
    analysis_lines.append("   • 为每种算法优化预处理")
    analysis_lines.append("   • 测试算法对预处理的鲁棒性")
    analysis_lines.append("")
    
    # 结论
    analysis_lines.append("🎯 结论")
    analysis_lines.append("=" * 15)
    analysis_lines.append("预处理管道的负面影响是多因素综合作用的结果:")
    analysis_lines.append("• 主要原因: 过度滤波和不当标准化")
    analysis_lines.append("• 次要原因: RLS自适应滤波器和算法特异性")
    analysis_lines.append("• 根本原因: 预处理设计未考虑数据集特性")
    analysis_lines.append("")
    analysis_lines.append("建议立即停用当前预处理管道，")
    analysis_lines.append("重新设计更适合PhysioNet数据集的预处理方法。")
    
    # 保存分析报告
    report_content = "\n".join(analysis_lines)
    
    with open("preprocessing_negative_impact_analysis.txt", 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("预处理负面影响分析报告已生成: preprocessing_negative_impact_analysis.txt")
    print("\n" + report_content)

def generate_preprocessing_recommendations():
    """生成新的预处理建议"""
    
    recommendations = {
        "immediate_actions": {
            "disable_current_preprocessing": True,
            "use_minimal_preprocessing": {
                "bandpass_filter": "1-50Hz (更宽频段)",
                "notch_filter": "可选，仅在必要时使用",
                "normalization": "通道级别标准化，避免时间窗口标准化",
                "adaptive_filter": "移除RLS自适应滤波器"
            }
        },
        "algorithm_specific_preprocessing": {
            "fbcsp_svm": {
                "bandpass": "8-30Hz (保持原有)",
                "normalization": "无需额外标准化",
                "special_notes": "FBCSP内置频段滤波"
            },
            "tef_rf": {
                "bandpass": "1-50Hz (保留更多信息)",
                "normalization": "特征级别标准化",
                "special_notes": "时域特征对预处理敏感"
            },
            "riemannian_meanfield": {
                "bandpass": "1-40Hz",
                "normalization": "无标准化",
                "special_notes": "协方差矩阵需要原始幅度信息"
            },
            "tangent_space_lr": {
                "bandpass": "1-40Hz",
                "normalization": "切空间后标准化",
                "special_notes": "流形结构敏感"
            },
            "plv_svm": {
                "bandpass": "8-30Hz",
                "normalization": "相位信息无需标准化",
                "special_notes": "相位锁定值对幅度不敏感"
            }
        },
        "validation_protocol": {
            "cross_validation": "严格的时间序列交叉验证",
            "preprocessing_in_cv": "预处理参数在每折内独立计算",
            "parameter_optimization": "网格搜索最优预处理参数"
        }
    }
    
    # 保存建议
    with open("new_preprocessing_recommendations.json", 'w', encoding='utf-8') as f:
        json.dump(recommendations, f, indent=2, ensure_ascii=False)
    
    print("新预处理建议已保存: new_preprocessing_recommendations.json")
    return recommendations

if __name__ == "__main__":
    # 生成分析报告
    analyze_preprocessing_negative_impact()
    
    # 生成改进建议
    recommendations = generate_preprocessing_recommendations()
    
    print("\n" + "="*60)
    print("分析完成！主要发现:")
    print("1. 预处理管道过度复杂，不适合PhysioNet数据集")
    print("2. 8-30Hz滤波器过于严格，损失有用信息")
    print("3. RLS自适应滤波器引入不必要的复杂性")
    print("4. Z-score标准化破坏了重要的信号特征")
    print("5. 不同算法对预处理的敏感性差异很大")
    print("="*60)
