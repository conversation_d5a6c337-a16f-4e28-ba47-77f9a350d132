# 设备硬件接口开发文档


## 设备接口

### 脑电设备接口

#### 硬件规格
- **芯片**: ADS1299 (8通道24位ADC)
- **采样率**: 125Hz
- **通道数**: 8通道
- **电极位置**: PZ, P3, P4, C3, CZ, C4, F3, F4 (10-20标准)
- **通信接口**: 串口 (默认COM8, 115200波特率)

#### 数据包格式
```
包头: 0x5A 0xA5 (2字节)
数据: 4组 × 24字节 = 96字节 (每组8通道×3字节)
包尾: 0x0D 0x0A (2字节)
总长度: 100字节
```

#### 控制命令
- **开始采集**: "START"
- **停止采集**: "STOP"
- **连接超时**: 3秒
- **最大重试**: 3次

### 电刺激设备接口

#### 硬件规格
- **DLL库**: RecoveryDLL.dll
- **通道数**: 2通道 (A通道、B通道)
- **电流范围**: 1-100mA (步长1mA)
- **频率范围**: 2-160Hz
- **脉宽范围**: 10-500μs

#### 参数配置
```python
STIMULATION_CONFIG = {
    'max_current': 100,           # 最大电流(mA)
    'min_current': 1,             # 最小电流(mA)
    'current_step': 1,            # 电流步长(mA)
    'default_frequency': 20,      # 默认频率(Hz)
    'default_pulse_width': 200,   # 默认脉宽(μs)
    'default_relax_time': 5,      # 默认休息时间(s)
    'default_climb_time': 2,      # 默认上升时间(s)
    'default_work_time': 10,      # 默认工作时间(s)
    'default_fall_time': 2,       # 默认下降时间(s)
    'port_num': 1,                # 端口号
    'connection_timeout': 5       # 连接超时(s)
}
```

#### DLL接口函数
- **OpenRecPort()**: 打开设备端口
- **CloseRecPort()**: 关闭设备端口
- **IsRecOpen()**: 检查设备状态
- **SetRecParams()**: 设置刺激参数
- **StartStimulation()**: 开始刺激
- **StopStimulation()**: 停止刺激

## 网络通信

### HTTP通信
- **服务器地址**: http://*************:8082/shdekf/Api/
- **超时时间**: 30秒
- **重试次数**: 1次
- **数据格式**: JSON

#### API接口
1. **患者数据上传**: `/uploadPatient`
2. **治疗数据上传**: `/uploadTreatment`
3. **设备状态上传**: `/updateEquipment`

### UDP通信
- **VR系统地址**: 127.0.0.1:3004
- **本地绑定端口**: 3005
- **重试次数**: 3次
- **重试间隔**: 0.01秒

#### UDP指令格式
```python
# 治疗指令
commands = {
    'treat': '开始治疗准备',
    'start': '开始电刺激',
    'stop': '停止电刺激',
    'stopall': '结束治疗'
}
```

## 配置管理

### 配置文件结构
```json
{
  "database": {
    "type": "sqlite",
    "path": "data/nk_system.db",
    "backup_path": "data/backup",
    "auto_backup": true,
    "backup_interval": 86400,
    "min_treatment_duration": 1
  },
  "stimulation": {
    "dll_path": "libs/RecoveryDLL.dll",
    "max_current": 100,
    "min_current": 1,
    "current_step": 1,
    "port_num": 7,
    "connection_timeout": 5
  },
  "eeg": {
    "serial_port": "COM8",
    "baud_rate": 115200,
    "sample_rate": 125.0,
    "channels": 8,
    "timeout": 5.0,
    "channel_names": ["PZ", "P3", "P4", "C3", "CZ", "C4", "F3", "F4"]
  },
  "signal_processing": {
    "filter_config": {
      "highpass_freq": 0.5,
      "lowpass_freq": 50.0,
      "notch_freq": 50.0,
      "filter_order": 4
    },
    "deep_learning": {
      "model_type": "eegnet",
      "training_samples": 100,
      "epochs": 50,
      "batch_size": 32,
      "learning_rate": 0.001,
      "temperature": 1.0,
      "dropout_rate": 0.25
    }
  }
}
```


## 权限管理

### 用户角色
1. **系统管理员 (admin)**: 所有权限
2. **医生 (doctor)**: 患者管理、治疗操作、报告生成、数据分析
3. **技师 (technician)**: 治疗操作、设备控制、数据采集
4. **操作员 (operator)**: 患者基本信息、治疗操作

### 密码策略
- **最小长度**: 6位
- **必须包含数字**: 是
- **密码有效期**: 90天
- **大小写要求**: 否
- **特殊字符要求**: 否

### 权限验证
```python
def check_permission(user_role: str, required_permission: str) -> bool:
    """检查用户权限"""
    role_permissions = PERMISSION_CONFIG['roles'].get(user_role, {})
    permissions = role_permissions.get('permissions', [])
    return 'all' in permissions or required_permission in permissions
```
