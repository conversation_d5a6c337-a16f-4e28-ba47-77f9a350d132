"""
多轮训练管理器

负责管理脑机接口系统的多轮训练流程，包括：
- 训练轮次管理和数据累积
- 历史数据加载和合并
- 版本化特征文件管理
- 训练性能跟踪和评估
"""

import os
import json
import time
import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class TrainingRoundInfo:
    """训练轮次信息"""
    round_number: int
    timestamp: str
    data_samples: int
    feature_files: Dict[str, str]
    classifier_files: Dict[str, str]  # 新增分类器文件
    performance_metrics: Dict[str, float]
    training_config: Dict[str, Any]
    notes: str = ""

class MultiRoundTrainingManager:
    """
    多轮训练管理器
    
    实现增量训练模式，每轮训练都累积历史数据，逐步改善模型性能。
    支持版本化文件管理、性能跟踪和训练历史记录。
    """
    
    def __init__(self, patient_id: str, base_dir: str = None):
        """
        初始化多轮训练管理器

        Args:
            patient_id: 患者ID
            base_dir: 数据基础目录，如果为None则使用路径管理器
        """
        self.patient_id = patient_id

        # 如果没有指定base_dir，使用路径管理器获取data目录
        if base_dir is None:
            from utils.path_manager import get_data_dir_path
            data_dir = get_data_dir_path()
            if data_dir is None:
                raise ValueError("无法获取数据目录路径")
            self.base_dir = str(data_dir)
        else:
            self.base_dir = base_dir
        
        # 目录设置
        self.features_dir = os.path.join(self.base_dir, "features")
        self.history_dir = os.path.join(self.base_dir, "training_history")
        self.raw_data_dir = os.path.join(self.base_dir, "raw_training_data")
        
        # 确保目录存在
        from utils.path_manager import path_manager
        for directory in [self.features_dir, self.history_dir, self.raw_data_dir]:
            path_manager.ensure_directory_exists(directory)
        
        # 训练历史
        self.training_history: List[TrainingRoundInfo] = []
        self.current_round = 0
        
        # 累积数据
        self.accumulated_data = {'X': [], 'y': []}
        
        # 历史文件路径
        self.history_file = os.path.join(self.history_dir, f"{patient_id}_training_history.json")
        
        # 加载历史记录
        self._load_training_history()
        
        logger.info(f"多轮训练管理器初始化完成，患者ID: {patient_id}, 历史轮次: {len(self.training_history)}")
    
    def get_next_round_number(self) -> int:
        """获取下一轮训练的轮次号"""
        if not self.training_history:
            return 1
        return max(round_info.round_number for round_info in self.training_history) + 1
    
    def has_training_history(self) -> bool:
        """检查是否有训练历史"""
        return len(self.training_history) > 0
    
    def get_latest_round_info(self) -> Optional[TrainingRoundInfo]:
        """获取最新轮次信息"""
        if not self.training_history:
            return None
        return max(self.training_history, key=lambda x: x.round_number)
    
    def load_historical_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载所有历史训练数据
        
        Returns:
            (X, y): 累积的训练数据和标签
        """
        try:
            all_X = []
            all_y = []
            
            # 加载每轮的原始数据
            for round_info in self.training_history:
                round_data_file = os.path.join(
                    self.raw_data_dir, 
                    f"{self.patient_id}_round_{round_info.round_number}_data.npz"
                )
                
                if os.path.exists(round_data_file):
                    data = np.load(round_data_file)
                    all_X.append(data['X'])
                    all_y.append(data['y'])
                    logger.debug(f"加载第{round_info.round_number}轮数据: {data['X'].shape}")
                else:
                    logger.warning(f"第{round_info.round_number}轮数据文件不存在: {round_data_file}")
            
            if all_X:
                X_combined = np.concatenate(all_X, axis=0)
                y_combined = np.concatenate(all_y, axis=0)
                logger.info(f"历史数据加载完成，总样本数: {X_combined.shape[0]}")
                return X_combined, y_combined
            else:
                logger.info("没有找到历史数据")
                return np.array([]), np.array([])
                
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            return np.array([]), np.array([])
    
    def start_new_round(self, training_config: Dict[str, Any] = None) -> int:
        """
        开始新一轮训练
        
        Args:
            training_config: 训练配置参数
            
        Returns:
            新轮次的编号
        """
        try:
            self.current_round = self.get_next_round_number()
            
            # 加载历史数据
            historical_X, historical_y = self.load_historical_data()
            
            # 重置累积数据
            self.accumulated_data = {'X': [], 'y': []}
            
            # 如果有历史数据，添加到累积数据中
            if historical_X.size > 0:
                self.accumulated_data['X'].append(historical_X)
                self.accumulated_data['y'].append(historical_y)
                logger.info(f"第{self.current_round}轮训练开始，累积历史样本: {historical_X.shape[0]}")
            else:
                logger.info(f"第{self.current_round}轮训练开始（首轮训练）")
            
            return self.current_round
            
        except Exception as e:
            logger.error(f"开始新轮次训练失败: {e}")
            raise
    
    def add_training_data(self, X: np.ndarray, y: np.ndarray):
        """
        添加新的训练数据到当前轮次
        
        Args:
            X: 训练数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]
        """
        try:
            self.accumulated_data['X'].append(X)
            self.accumulated_data['y'].append(y)
            logger.info(f"第{self.current_round}轮添加训练数据: {X.shape}")
            
        except Exception as e:
            logger.error(f"添加训练数据失败: {e}")
            raise
    
    def get_accumulated_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        获取当前累积的所有训练数据
        
        Returns:
            (X, y): 累积的训练数据和标签
        """
        try:
            if not self.accumulated_data['X']:
                return np.array([]), np.array([])
            
            X_all = np.concatenate(self.accumulated_data['X'], axis=0)
            y_all = np.concatenate(self.accumulated_data['y'], axis=0)
            
            logger.debug(f"累积数据形状: {X_all.shape}")
            return X_all, y_all
            
        except Exception as e:
            logger.error(f"获取累积数据失败: {e}")
            return np.array([]), np.array([])
    
    def complete_round(self, feature_files: Dict[str, str],
                      classifier_files: Dict[str, str] = None,
                      performance_metrics: Dict[str, float] = None,
                      training_config: Dict[str, Any] = None,
                      notes: str = "") -> TrainingRoundInfo:
        """
        完成当前轮次训练
        
        Args:
            feature_files: 保存的特征文件路径字典
            classifier_files: 保存的分类器文件路径字典
            performance_metrics: 性能指标
            training_config: 训练配置
            notes: 备注信息
            
        Returns:
            当前轮次信息
        """
        try:
            # 获取当前轮次的数据
            X_all, y_all = self.get_accumulated_data()
            
            if X_all.size == 0:
                raise ValueError("没有训练数据")
            
            # 保存原始数据
            self._save_round_data(X_all, y_all)
            
            # 创建轮次信息
            round_info = TrainingRoundInfo(
                round_number=self.current_round,
                timestamp=datetime.now().isoformat(),
                data_samples=X_all.shape[0],
                feature_files=feature_files.copy(),
                classifier_files=classifier_files.copy() if classifier_files else {},
                performance_metrics=performance_metrics or {},
                training_config=training_config or {},
                notes=notes
            )
            
            # 添加到历史记录
            self.training_history.append(round_info)
            
            # 保存历史记录
            self._save_training_history()
            
            logger.info(f"第{self.current_round}轮训练完成，样本数: {X_all.shape[0]}")
            
            return round_info
            
        except Exception as e:
            logger.error(f"完成轮次训练失败: {e}")
            raise
    
    def _save_round_data(self, X: np.ndarray, y: np.ndarray):
        """保存轮次原始数据"""
        try:
            # 只保存当前轮次的新数据（不包括历史数据）
            if len(self.accumulated_data['X']) > 1:
                # 如果有历史数据，只保存最后添加的新数据
                X_new = self.accumulated_data['X'][-1]
                y_new = self.accumulated_data['y'][-1]
            else:
                # 如果是首轮训练，保存全部数据
                X_new = X
                y_new = y
            
            data_file = os.path.join(
                self.raw_data_dir,
                f"{self.patient_id}_round_{self.current_round}_data.npz"
            )
            
            np.savez_compressed(data_file, X=X_new, y=y_new)
            logger.debug(f"轮次数据已保存: {data_file}")
            
        except Exception as e:
            logger.error(f"保存轮次数据失败: {e}")
            raise
    
    def _load_training_history(self):
        """加载训练历史"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history_data = json.load(f)
                
                self.training_history = [
                    TrainingRoundInfo(**round_data) 
                    for round_data in history_data
                ]
                
                logger.info(f"训练历史加载完成，共{len(self.training_history)}轮")
            else:
                logger.info("没有找到训练历史文件")
                
        except Exception as e:
            logger.error(f"加载训练历史失败: {e}")
            self.training_history = []
    
    def _save_training_history(self):
        """保存训练历史"""
        try:
            history_data = [asdict(round_info) for round_info in self.training_history]
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"训练历史已保存: {self.history_file}")
            
        except Exception as e:
            logger.error(f"保存训练历史失败: {e}")
            raise
    
    def get_training_summary(self) -> Dict[str, Any]:
        """获取训练摘要信息"""
        try:
            if not self.training_history:
                return {
                    'total_rounds': 0,
                    'total_samples': 0,
                    'latest_performance': {},
                    'performance_trend': []
                }
            
            total_samples = sum(round_info.data_samples for round_info in self.training_history)
            latest_round = self.get_latest_round_info()
            
            # 性能趋势（如果有性能指标）
            performance_trend = []
            for round_info in self.training_history:
                if round_info.performance_metrics:
                    performance_trend.append({
                        'round': round_info.round_number,
                        'metrics': round_info.performance_metrics
                    })
            
            return {
                'total_rounds': len(self.training_history),
                'total_samples': total_samples,
                'latest_performance': latest_round.performance_metrics if latest_round else {},
                'performance_trend': performance_trend,
                'latest_timestamp': latest_round.timestamp if latest_round else None
            }
            
        except Exception as e:
            logger.error(f"获取训练摘要失败: {e}")
            return {}
