"""
预处理管道影响测试脚本

对比有无预处理管道的5类算法性能差异
验证预处理管道对算法性能的实际影响
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import json
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from services.validation.test_5_algorithms import FiveAlgorithmsValidator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('validation_preprocessing_impact.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class PreprocessingImpactValidator:
    """预处理管道影响验证器"""
    
    def __init__(self, dataset_path: str):
        """
        初始化预处理影响验证器
        
        Args:
            dataset_path: EEG数据集路径
        """
        self.dataset_path = dataset_path
        
        # 测试前3名受试者（快速验证）
        self.test_subjects = [f"S{i:03d}" for i in range(1, 4)]
        
        logger.info(f"预处理影响验证器初始化完成")
        logger.info(f"数据集路径: {dataset_path}")
        logger.info(f"测试受试者: {self.test_subjects}")
    
    def run_comparison_test(self, cv_folds: int = 3) -> Dict[str, Any]:
        """
        运行预处理对比测试
        
        Args:
            cv_folds: 交叉验证折数
            
        Returns:
            对比测试结果
        """
        logger.info("开始预处理管道影响对比测试")
        logger.info(f"测试受试者数量: {len(self.test_subjects)}")
        logger.info(f"交叉验证折数: {cv_folds}")
        
        # 测试无预处理版本
        logger.info("=" * 60)
        logger.info("测试无预处理版本...")
        logger.info("=" * 60)
        
        validator_no_preprocessing = FiveAlgorithmsValidator(
            self.dataset_path,
            results_dir="validation_results_no_preprocessing",
            enable_preprocessing=False
        )
        
        results_no_preprocessing = self._run_limited_validation(
            validator_no_preprocessing, cv_folds
        )
        
        # 测试有预处理版本
        logger.info("=" * 60)
        logger.info("测试有预处理版本...")
        logger.info("=" * 60)
        
        validator_with_preprocessing = FiveAlgorithmsValidator(
            self.dataset_path,
            results_dir="validation_results_with_preprocessing",
            enable_preprocessing=True
        )
        
        results_with_preprocessing = self._run_limited_validation(
            validator_with_preprocessing, cv_folds
        )
        
        # 生成对比报告
        comparison_report = self._generate_comparison_report(
            results_no_preprocessing,
            results_with_preprocessing
        )
        
        # 保存结果
        self._save_comparison_results(comparison_report)
        
        logger.info("预处理管道影响对比测试完成")
        return comparison_report
    
    def _run_limited_validation(self, validator: FiveAlgorithmsValidator, cv_folds: int) -> Dict[str, Any]:
        """运行限制受试者的验证"""
        # 检查数据集可用性
        available_subjects = []
        
        for subject_id in self.test_subjects:
            subject_path = os.path.join(self.dataset_path, subject_id)
            if os.path.exists(subject_path):
                # 检查必需的文件
                required_files = [
                    f"{subject_id}R06.edf",
                    f"{subject_id}R06.edf.event", 
                    f"{subject_id}R10.edf",
                    f"{subject_id}R10.edf.event"
                ]
                
                files_exist = all(
                    os.path.exists(os.path.join(subject_path, file)) 
                    for file in required_files
                )
                
                if files_exist:
                    available_subjects.append(subject_id)
                    logger.info(f"✓ {subject_id}: 数据文件完整")
                else:
                    logger.warning(f"✗ {subject_id}: 缺少必需文件")
            else:
                logger.warning(f"✗ {subject_id}: 目录不存在")
        
        if not available_subjects:
            raise ValueError("没有找到可用的受试者数据")
        
        # 执行验证
        results = validator.validator.validate_multiple_subjects(available_subjects, cv_folds)
        return results
    
    def _generate_comparison_report(self, results_no_prep: Dict[str, Any], 
                                   results_with_prep: Dict[str, Any]) -> Dict[str, Any]:
        """生成对比报告"""
        logger.info("生成预处理影响对比报告...")
        
        # 算法名称映射
        algorithm_names = {
            'fbcsp_svm': 'FBCSP + SVM',
            'tef_rf': 'TEF + RandomForest',
            'riemannian_meanfield': 'Riemannian + MeanField',
            'tangent_space_lr': 'TangentSpace + LogisticRegression',
            'plv_svm': 'PLV + SVM'
        }
        
        comparison_report = {
            'test_info': {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'dataset_path': self.dataset_path,
                'tested_subjects': self.test_subjects,
                'total_subjects': len(self.test_subjects),
                'test_purpose': '预处理管道影响评估'
            },
            'no_preprocessing_results': results_no_prep,
            'with_preprocessing_results': results_with_prep,
            'performance_comparison': {},
            'preprocessing_impact_summary': {}
        }
        
        # 性能对比分析
        if ('algorithms' in results_no_prep and 'algorithms' in results_with_prep):
            for alg_key, alg_name in algorithm_names.items():
                if (alg_key in results_no_prep['algorithms'] and 
                    alg_key in results_with_prep['algorithms']):
                    
                    no_prep_data = results_no_prep['algorithms'][alg_key]
                    with_prep_data = results_with_prep['algorithms'][alg_key]
                    
                    no_prep_acc = no_prep_data.get('mean_cv_accuracy', 0)
                    with_prep_acc = with_prep_data.get('mean_cv_accuracy', 0)
                    
                    improvement = with_prep_acc - no_prep_acc
                    improvement_percent = (improvement / no_prep_acc * 100) if no_prep_acc > 0 else 0
                    
                    comparison_report['performance_comparison'][alg_name] = {
                        'no_preprocessing_accuracy': no_prep_acc,
                        'with_preprocessing_accuracy': with_prep_acc,
                        'improvement': improvement,
                        'improvement_percent': improvement_percent,
                        'algorithm_key': alg_key
                    }
        
        # 总体影响摘要
        if comparison_report['performance_comparison']:
            improvements = [
                data['improvement'] for data in comparison_report['performance_comparison'].values()
                if 'improvement' in data
            ]

            if improvements:
                avg_improvement = np.mean(improvements)
                improved_count = sum(1 for imp in improvements if imp > 0)
                degraded_count = sum(1 for imp in improvements if imp < 0)

                comparison_report['preprocessing_impact_summary'] = {
                    'average_improvement': avg_improvement,
                    'improved_algorithms': improved_count,
                    'degraded_algorithms': degraded_count,
                    'total_algorithms': len(improvements),
                    'overall_impact': 'positive' if avg_improvement > 0 else 'negative'
                }
            else:
                comparison_report['preprocessing_impact_summary'] = {
                    'average_improvement': 0.0,
                    'improved_algorithms': 0,
                    'degraded_algorithms': 0,
                    'total_algorithms': 0,
                    'overall_impact': 'unknown'
                }
        
        return comparison_report
    
    def _save_comparison_results(self, comparison_report: Dict[str, Any]):
        """保存对比结果"""
        # 保存详细报告
        report_file = "preprocessing_impact_comparison_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(comparison_report, f, indent=2, ensure_ascii=False)
        
        # 生成简化的文本报告
        self._save_text_summary(comparison_report)
        
        logger.info(f"预处理影响对比报告已保存: {report_file}")
    
    def _save_text_summary(self, comparison_report: Dict[str, Any]):
        """保存文本摘要"""
        try:
            summary_lines = []
            
            # 报告头部
            summary_lines.append("预处理管道影响评估报告")
            summary_lines.append("=" * 50)
            summary_lines.append(f"生成时间: {comparison_report['test_info']['timestamp']}")
            summary_lines.append(f"测试受试者: {len(comparison_report['test_info']['tested_subjects'])}名")
            summary_lines.append("")
            
            # 性能对比表格
            if 'performance_comparison' in comparison_report:
                summary_lines.append("算法性能对比表")
                summary_lines.append("-" * 70)
                summary_lines.append(f"{'算法名称':<30} {'无预处理':<12} {'有预处理':<12} {'提升幅度':<15}")
                summary_lines.append("-" * 70)
                
                for alg_name, data in comparison_report['performance_comparison'].items():
                    no_prep = f"{data['no_preprocessing_accuracy']:.3f}"
                    with_prep = f"{data['with_preprocessing_accuracy']:.3f}"
                    
                    if data['improvement'] >= 0:
                        improvement = f"+{data['improvement']:.3f} ({data['improvement_percent']:+.1f}%)"
                    else:
                        improvement = f"{data['improvement']:.3f} ({data['improvement_percent']:+.1f}%)"
                    
                    summary_lines.append(f"{alg_name:<30} {no_prep:<12} {with_prep:<12} {improvement:<15}")
                
                summary_lines.append("-" * 70)
            
            # 总体影响摘要
            if 'preprocessing_impact_summary' in comparison_report:
                summary = comparison_report['preprocessing_impact_summary']
                summary_lines.append("")
                summary_lines.append("总体影响摘要")
                summary_lines.append("=" * 30)
                summary_lines.append(f"平均性能提升: {summary['average_improvement']:.3f}")
                summary_lines.append(f"性能提升算法: {summary['improved_algorithms']}/{summary['total_algorithms']}")
                summary_lines.append(f"性能下降算法: {summary['degraded_algorithms']}/{summary['total_algorithms']}")
                summary_lines.append(f"总体影响: {summary['overall_impact']}")
            
            # 保存文本摘要
            summary_file = "preprocessing_impact_summary.txt"
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("\n".join(summary_lines))
            
            logger.info(f"文本摘要已保存: {summary_file}")
            
        except Exception as e:
            logger.warning(f"保存文本摘要失败: {e}")


def main():
    """主函数"""
    # 数据集路径
    dataset_path = r"D:\脑电\数据\EEG Motor MovementImagery Dataset"
    
    # 检查数据集路径
    if not os.path.exists(dataset_path):
        logger.error(f"数据集路径不存在: {dataset_path}")
        return
    
    try:
        # 创建验证器
        validator = PreprocessingImpactValidator(dataset_path)
        
        # 执行对比测试
        results = validator.run_comparison_test(cv_folds=3)
        
        # 打印简要结果
        print("\n" + "="*60)
        print("预处理管道影响评估结果摘要")
        print("="*60)
        
        if 'preprocessing_impact_summary' in results:
            summary = results['preprocessing_impact_summary']
            print(f"\n总体影响: {summary.get('overall_impact', 'unknown')}")
            print(f"平均性能提升: {summary.get('average_improvement', 0):.3f}")
            print(f"性能提升算法: {summary.get('improved_algorithms', 0)}/{summary.get('total_algorithms', 0)}")
            print(f"性能下降算法: {summary.get('degraded_algorithms', 0)}/{summary.get('total_algorithms', 0)}")

        if 'performance_comparison' in results:
            print("\n各算法影响:")
            for alg_name, data in results['performance_comparison'].items():
                improvement = data.get('improvement', 0)
                improvement_percent = data.get('improvement_percent', 0)
                impact = "↑" if improvement > 0 else "↓" if improvement < 0 else "→"
                print(f"{impact} {alg_name}: {improvement_percent:+.1f}%")
        
        print("\n详细结果已保存到当前目录")
        print("="*60)
        
    except Exception as e:
        logger.error(f"预处理影响测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
