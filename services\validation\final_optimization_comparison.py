"""
最终优化对比分析脚本

对比三个版本的性能：
1. 原始配置 (4-40Hz + 原始参数)
2. 8-30Hz带通滤波器 (8-30Hz + 原始参数)  
3. 8-30Hz完整优化 (8-30Hz + 优化参数)
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime

def load_results(file_path: str) -> dict:
    """加载JSON结果文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"文件不存在: {file_path}")
        return {}

def extract_performance_from_preprocessing_results(results: dict) -> dict:
    """从预处理影响测试结果中提取4-40Hz性能"""
    performance = {}
    
    algorithm_mapping = {
        'fbcsp_svm': 'FBCSP + SVM',
        'tef_rf': 'TEF + RandomForest',
        'riemannian_meanfield': 'Riemannian + MeanField',
        'tangent_space_lr': 'TangentSpace + LogisticRegression',
        'plv_svm': 'PLV + SVM'
    }
    
    if 'with_preprocessing_results' in results and 'algorithms' in results['with_preprocessing_results']:
        algorithms = results['with_preprocessing_results']['algorithms']
        
        for alg_key, alg_name in algorithm_mapping.items():
            if alg_key in algorithms:
                alg_data = algorithms[alg_key]
                mean_acc = alg_data.get('mean_cv_accuracy', 0)
                std_acc = alg_data.get('std_cv_accuracy', 0)
                performance[alg_name] = {'accuracy': mean_acc, 'std': std_acc}
    
    return performance

def extract_performance_from_bandpass_results(results: dict) -> dict:
    """从带通优化测试结果中提取8-30Hz性能"""
    performance = {}
    
    algorithm_mapping = {
        'fbcsp_svm': 'FBCSP + SVM',
        'tef_rf': 'TEF + RandomForest',
        'riemannian_meanfield': 'Riemannian + MeanField',
        'tangent_space_lr': 'TangentSpace + LogisticRegression',
        'plv_svm': 'PLV + SVM'
    }
    
    if 'optimized_results' in results and 'algorithms' in results['optimized_results']:
        algorithms = results['optimized_results']['algorithms']
        
        for alg_key, alg_name in algorithm_mapping.items():
            if alg_key in algorithms:
                alg_data = algorithms[alg_key]
                mean_acc = alg_data.get('mean_cv_accuracy', 0)
                std_acc = alg_data.get('std_cv_accuracy', 0)
                performance[alg_name] = {'accuracy': mean_acc, 'std': std_acc}
    
    return performance

def extract_performance_from_optimized_results(results: dict) -> dict:
    """从完整优化测试结果中提取性能"""
    performance = {}
    
    algorithm_mapping = {
        'fbcsp_svm': 'FBCSP + SVM',
        'tef_rf': 'TEF + RandomForest',
        'riemannian_meanfield': 'Riemannian + MeanField',
        'tangent_space_lr': 'TangentSpace + LogisticRegression',
        'plv_svm': 'PLV + SVM'
    }
    
    if 'optimized_results' in results and 'algorithms' in results['optimized_results']:
        algorithms = results['optimized_results']['algorithms']
        
        for alg_key, alg_name in algorithm_mapping.items():
            if alg_key in algorithms:
                alg_data = algorithms[alg_key]
                mean_acc = alg_data.get('mean_cv_accuracy', 0)
                std_acc = alg_data.get('std_cv_accuracy', 0)
                performance[alg_name] = {'accuracy': mean_acc, 'std': std_acc}
    
    return performance

def generate_final_comparison_analysis():
    """生成最终对比分析报告"""
    
    # 加载三个版本的结果数据
    preprocessing_results = load_results("preprocessing_impact_comparison_report.json")
    bandpass_results = load_results("bandpass_optimization_report.json")
    optimized_results = load_results("optimized_8_30Hz_report.json")
    
    # 提取性能数据
    performance_4_40 = extract_performance_from_preprocessing_results(preprocessing_results)
    performance_8_30_basic = extract_performance_from_bandpass_results(bandpass_results)
    performance_8_30_optimized = extract_performance_from_optimized_results(optimized_results)
    
    if not all([performance_4_40, performance_8_30_basic, performance_8_30_optimized]):
        print("无法加载完整的性能数据")
        return
    
    # 生成对比分析
    analysis_lines = []
    
    # 报告头部
    analysis_lines.append("最终优化对比分析报告")
    analysis_lines.append("=" * 80)
    analysis_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    analysis_lines.append("对比版本:")
    analysis_lines.append("  版本1: 原始配置 (4-40Hz + 原始参数)")
    analysis_lines.append("  版本2: 8-30Hz带通滤波器 (8-30Hz + 原始参数)")
    analysis_lines.append("  版本3: 8-30Hz完整优化 (8-30Hz + 优化参数)")
    analysis_lines.append("测试数据: 前3名受试者，5类算法")
    analysis_lines.append("")
    
    # 详细对比表格
    analysis_lines.append("详细性能对比表")
    analysis_lines.append("-" * 100)
    analysis_lines.append(f"{'算法名称':<30} {'版本1(4-40Hz)':<15} {'版本2(8-30Hz)':<15} {'版本3(优化)':<15} {'总提升':<15}")
    analysis_lines.append("-" * 100)
    
    total_improvements_v1_v3 = []
    total_improvements_v2_v3 = []
    
    for alg_name in performance_4_40.keys():
        if alg_name in performance_8_30_basic and alg_name in performance_8_30_optimized:
            acc_v1 = performance_4_40[alg_name]['accuracy']
            acc_v2 = performance_8_30_basic[alg_name]['accuracy']
            acc_v3 = performance_8_30_optimized[alg_name]['accuracy']
            
            improvement_v1_v3 = acc_v3 - acc_v1
            improvement_v2_v3 = acc_v3 - acc_v2
            improvement_v1_v3_percent = (improvement_v1_v3 / acc_v1 * 100) if acc_v1 > 0 else 0
            
            total_improvements_v1_v3.append(improvement_v1_v3)
            total_improvements_v2_v3.append(improvement_v2_v3)
            
            # 格式化显示
            acc_v1_str = f"{acc_v1:.3f}"
            acc_v2_str = f"{acc_v2:.3f}"
            acc_v3_str = f"{acc_v3:.3f}"
            
            if improvement_v1_v3 >= 0:
                total_improvement_str = f"+{improvement_v1_v3:.3f} ({improvement_v1_v3_percent:+.1f}%)"
            else:
                total_improvement_str = f"{improvement_v1_v3:.3f} ({improvement_v1_v3_percent:+.1f}%)"
            
            analysis_lines.append(f"{alg_name:<30} {acc_v1_str:<15} {acc_v2_str:<15} {acc_v3_str:<15} {total_improvement_str:<15}")
    
    analysis_lines.append("-" * 100)
    
    # 总体分析
    if total_improvements_v1_v3:
        avg_improvement_v1_v3 = np.mean(total_improvements_v1_v3)
        avg_improvement_v2_v3 = np.mean(total_improvements_v2_v3)
        improved_count_v1_v3 = sum(1 for imp in total_improvements_v1_v3 if imp > 0)
        improved_count_v2_v3 = sum(1 for imp in total_improvements_v2_v3 if imp > 0)
        
        analysis_lines.append("")
        analysis_lines.append("总体优化效果分析")
        analysis_lines.append("=" * 50)
        analysis_lines.append(f"版本1→版本3 平均提升: {avg_improvement_v1_v3:.3f}")
        analysis_lines.append(f"版本2→版本3 平均提升: {avg_improvement_v2_v3:.3f}")
        analysis_lines.append(f"版本1→版本3 提升算法: {improved_count_v1_v3}/5")
        analysis_lines.append(f"版本2→版本3 提升算法: {improved_count_v2_v3}/5")
        
        if avg_improvement_v1_v3 > 0:
            analysis_lines.append("✅ 总体优化效果: 正面")
        else:
            analysis_lines.append("❌ 总体优化效果: 负面")
    
    # 分阶段优化分析
    analysis_lines.append("")
    analysis_lines.append("分阶段优化分析")
    analysis_lines.append("=" * 50)
    
    for alg_name in performance_4_40.keys():
        if alg_name in performance_8_30_basic and alg_name in performance_8_30_optimized:
            acc_v1 = performance_4_40[alg_name]['accuracy']
            acc_v2 = performance_8_30_basic[alg_name]['accuracy']
            acc_v3 = performance_8_30_optimized[alg_name]['accuracy']
            
            stage1_improvement = acc_v2 - acc_v1  # 带通滤波器优化
            stage2_improvement = acc_v3 - acc_v2  # 参数优化
            total_improvement = acc_v3 - acc_v1   # 总优化
            
            analysis_lines.append(f"\n📊 {alg_name}:")
            analysis_lines.append(f"   版本1 (4-40Hz): {acc_v1:.3f}")
            analysis_lines.append(f"   版本2 (8-30Hz): {acc_v2:.3f} (阶段1: {stage1_improvement:+.3f})")
            analysis_lines.append(f"   版本3 (优化):   {acc_v3:.3f} (阶段2: {stage2_improvement:+.3f})")
            analysis_lines.append(f"   总提升: {total_improvement:+.3f}")
            
            # 分析主要贡献
            if abs(stage1_improvement) > abs(stage2_improvement):
                analysis_lines.append("   💡 主要贡献: 带通滤波器优化")
            elif abs(stage2_improvement) > abs(stage1_improvement):
                analysis_lines.append("   💡 主要贡献: 参数优化")
            else:
                analysis_lines.append("   💡 主要贡献: 两阶段均衡")
    
    # 优化策略总结
    analysis_lines.append("")
    analysis_lines.append("优化策略总结")
    analysis_lines.append("=" * 40)
    analysis_lines.append("🎯 阶段1优化 (带通滤波器 4-40Hz → 8-30Hz):")
    analysis_lines.append("   • 移除4-8Hz低频漂移和眼动伪迹")
    analysis_lines.append("   • 聚焦8-12Hz μ节律运动想象特征")
    analysis_lines.append("   • 保留13-30Hz β节律运动执行信息")
    analysis_lines.append("   • 移除30-40Hz肌电干扰和高频噪声")
    analysis_lines.append("")
    analysis_lines.append("🔧 阶段2优化 (参数优化):")
    analysis_lines.append("   • FBCSP: 扩展到3个频段，增加组件数")
    analysis_lines.append("   • TEF: 增加频域特征，扩展频段")
    analysis_lines.append("   • Riemannian: 改用oas估计器，降低正则化")
    analysis_lines.append("   • TangentSpace: 改用oas估计器，大幅降低正则化")
    analysis_lines.append("   • PLV: 扩展频段和通道对，增加特征数")
    
    # 最终建议
    analysis_lines.append("")
    analysis_lines.append("最终建议")
    analysis_lines.append("=" * 20)
    
    if avg_improvement_v1_v3 > 0:
        analysis_lines.append("✅ 建议采用版本3 (8-30Hz完整优化)")
        analysis_lines.append("   理由：总体性能提升，理论基础扎实")
    else:
        analysis_lines.append("⚠️  需要进一步分析")
        analysis_lines.append("   建议：检查特定算法的参数配置")
    
    analysis_lines.append("")
    analysis_lines.append("🚀 实施建议:")
    analysis_lines.append("   1. 立即应用8-30Hz带通滤波器")
    analysis_lines.append("   2. 逐步部署参数优化配置")
    analysis_lines.append("   3. 监控实际使用中的性能表现")
    analysis_lines.append("   4. 根据用户反馈进一步微调")
    
    # 保存分析报告
    report_content = "\n".join(analysis_lines)
    
    with open("final_optimization_comparison.txt", 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("最终优化对比分析报告已生成: final_optimization_comparison.txt")
    print("\n" + report_content)

if __name__ == "__main__":
    generate_final_comparison_analysis()
