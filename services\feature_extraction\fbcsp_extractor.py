"""
FBCSP (Filter Bank Common Spatial Pattern) 特征提取器

基于MNE-Python实现的滤波器组共同空间模式特征提取。
"""

import numpy as np
import logging
from typing import List, Tuple, Optional, Union
from sklearn.base import clone

try:
    import mne
    from mne.decoding import CSP
    from mne.filter import filter_data
    MNE_AVAILABLE = True
except ImportError:
    MNE_AVAILABLE = False
    mne = None
    CSP = None
    filter_data = None

from .base_extractor import BaseFeatureExtractor

logger = logging.getLogger(__name__)

class FBCSPExtractor(BaseFeatureExtractor):
    """
    FBCSP特征提取器
    
    实现滤波器组共同空间模式特征提取，将EEG信号分解为多个频段，
    对每个频段应用CSP算法提取空间特征。
    """
    
    def __init__(self,
                 freq_bands: List[Tuple[float, float]] = None,
                 n_components: int = 2,
                 sampling_rate: int = 125,
                 reg: Optional[str] = 'oas',
                 log: bool = True,
                 **kwargs):
        """
        初始化FBCSP特征提取器

        Args:
            freq_bands: 频段列表 [(low1, high1), (low2, high2), ...]
            n_components: 每个频段的CSP组件数
            sampling_rate: 采样率 (Hz)
            reg: CSP正则化方法 ('oas', 'ledoit_wolf', 'empirical', None)
            log: 是否对方差特征取对数
            **kwargs: 传递给基类的参数
        """
        super().__init__(sampling_rate=sampling_rate, **kwargs)
        
        if not MNE_AVAILABLE:
            raise ImportError("FBCSP需要安装MNE-Python: pip install mne")
        
        if freq_bands is None:
            # 默认6个频段，避免过拟合
            freq_bands = [
                (8, 12), (12, 16), (16, 20), (20, 24),
                (24, 30), (30, 35)
            ]
        
        self.freq_bands = freq_bands
        self.n_components = n_components
        self.reg = reg
        self.log = log

        # 验证频段
        self._validate_freq_bands()

        # CSP滤波器（每个频段一个）
        self._csp_filters = []
        
        # 设置MNE日志级别
        mne.set_log_level('WARNING')
        
        logger.info(f"FBCSP初始化完成，频段数: {len(self.freq_bands)}，"
                   f"每频段组件数: {self.n_components}")
    
    def _validate_freq_bands(self):
        """验证频段设置"""
        nyquist = self.sampling_rate / 2
        
        for i, (low, high) in enumerate(self.freq_bands):
            if low >= high:
                raise ValueError(f"频段{i}无效: 低频({low}) >= 高频({high})")
            
            if low < 0:
                raise ValueError(f"频段{i}低频不能小于0: {low}")
            
            if high > nyquist:
                raise ValueError(f"频段{i}高频({high})超过Nyquist频率({nyquist})")
            
            if low < 1:
                logger.warning(f"频段{i}低频({low})可能过低，建议 >= 1Hz")

    def _detect_bad_channels(self, X: np.ndarray) -> List[int]:
        """
        检测坏导通道

        Args:
            X: EEG数据 [n_trials, n_channels, n_samples]

        Returns:
            坏导通道索引列表
        """
        bad_channels = []
        n_trials, n_channels, n_samples = X.shape

        for ch in range(n_channels):
            channel_data = X[:, ch, :]

            # 检查1: 零值通道（方差接近0）
            channel_var = np.var(channel_data)
            if channel_var < 1e-10:
                bad_channels.append(ch)
                logger.warning(f"检测到零值坏导: 通道{ch} (方差={channel_var:.2e})")
                continue

            # 检查2: 极值通道（方差过大）
            # 计算所有通道的方差中位数作为参考
            all_vars = [np.var(X[:, i, :]) for i in range(n_channels)]
            median_var = np.median(all_vars)

            if channel_var > median_var * 100:  # 方差超过中位数100倍
                bad_channels.append(ch)
                logger.warning(f"检测到极值坏导: 通道{ch} (方差={channel_var:.2e}, 中位数={median_var:.2e})")
                continue

            # 检查3: 常数通道（所有值相同）
            if np.all(channel_data == channel_data[0, 0]):
                bad_channels.append(ch)
                logger.warning(f"检测到常数坏导: 通道{ch}")
                continue

        return bad_channels

    def _interpolate_bad_channels(self, X: np.ndarray, bad_channels: List[int]) -> np.ndarray:
        """
        插值修复坏导通道

        Args:
            X: EEG数据 [n_trials, n_channels, n_samples]
            bad_channels: 坏导通道索引列表

        Returns:
            修复后的数据
        """
        if not bad_channels:
            return X

        X_fixed = X.copy()
        n_trials, n_channels, n_samples = X.shape

        # 获取好导通道
        good_channels = [ch for ch in range(n_channels) if ch not in bad_channels]

        if len(good_channels) < 2:
            raise ValueError(f"好导通道太少({len(good_channels)})，无法进行插值修复")

        for bad_ch in bad_channels:
            logger.info(f"使用邻近通道插值修复通道{bad_ch}")

            # 简单的邻近通道平均插值
            if bad_ch == 0:
                # 第一个通道，使用后面的通道
                neighbor_channels = good_channels[:2] if len(good_channels) >= 2 else good_channels
            elif bad_ch == n_channels - 1:
                # 最后一个通道，使用前面的通道
                neighbor_channels = good_channels[-2:] if len(good_channels) >= 2 else good_channels
            else:
                # 中间通道，使用前后通道
                before_ch = [ch for ch in good_channels if ch < bad_ch]
                after_ch = [ch for ch in good_channels if ch > bad_ch]

                neighbor_channels = []
                if before_ch:
                    neighbor_channels.append(before_ch[-1])  # 最近的前一个好导
                if after_ch:
                    neighbor_channels.append(after_ch[0])   # 最近的后一个好导

                # 如果邻近通道不够，添加更多好导
                while len(neighbor_channels) < 2 and len(neighbor_channels) < len(good_channels):
                    for ch in good_channels:
                        if ch not in neighbor_channels:
                            neighbor_channels.append(ch)
                            break

            # 执行插值
            if neighbor_channels:
                X_fixed[:, bad_ch, :] = np.mean(X[:, neighbor_channels, :], axis=1)
                logger.debug(f"通道{bad_ch}已使用通道{neighbor_channels}进行插值修复")

        return X_fixed
    
    def _apply_bandpass_filter(self, data: np.ndarray, low: float, high: float) -> np.ndarray:
        """
        应用带通滤波器
        
        Args:
            data: 输入数据 [n_trials, n_channels, n_samples]
            low: 低频截止频率
            high: 高频截止频率
            
        Returns:
            滤波后的数据
        """
        try:
            # MNE的filter_data期望数据形状为 [n_channels, n_samples]
            # 我们需要对每个trial分别滤波
            filtered_data = np.zeros_like(data)

            for trial in range(data.shape[0]):
                # 确保数据类型为float64
                trial_data = data[trial].astype(np.float64)
                filtered_data[trial] = filter_data(
                    trial_data,
                    sfreq=self.sampling_rate,
                    l_freq=low,
                    h_freq=high,
                    method='iir',
                    iir_params={'order': 4, 'ftype': 'butter'},
                    verbose=False
                )
            
            return filtered_data
            
        except Exception as e:
            logger.error(f"带通滤波失败 ({low}-{high}Hz): {e}")
            raise
    
    def _extract_csp_features(self, data: np.ndarray, csp_filter: CSP) -> np.ndarray:
        """
        使用CSP滤波器提取特征

        Args:
            data: 输入数据 [n_trials, n_channels, n_samples]
            csp_filter: 训练好的CSP滤波器

        Returns:
            CSP特征 [n_trials, n_components]
        """
        try:
            # 应用CSP变换
            csp_data = csp_filter.transform(data)

            # 检查CSP输出的维度
            logger.debug(f"CSP变换后数据形状: {csp_data.shape}")

            # 计算方差特征
            if csp_data.ndim == 3:
                # 标准情况：[n_trials, n_components, n_samples]
                features = np.var(csp_data, axis=2)
            elif csp_data.ndim == 2:
                # 可能的情况：[n_trials, n_features] (已经是特征)
                features = csp_data
            else:
                raise ValueError(f"意外的CSP输出维度: {csp_data.shape}")

            # 对数变换（可选）
            if self.log and csp_data.ndim == 3:
                features = np.log(features + 1e-8)  # 添加小常数避免log(0)

            return features

        except Exception as e:
            logger.error(f"CSP特征提取失败: {e}")
            raise
    
    def _fit(self, X: np.ndarray, y: np.ndarray) -> 'FBCSPExtractor':
        """
        拟合FBCSP模型
        
        Args:
            X: 训练数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]
            
        Returns:
            self
        """
        try:
            logger.info(f"开始训练FBCSP，数据形状: {X.shape}")

            # 检测和修复坏导
            bad_channels = self._detect_bad_channels(X)
            if bad_channels:
                logger.warning(f"检测到{len(bad_channels)}个坏导通道: {bad_channels}")
                X = self._interpolate_bad_channels(X, bad_channels)
                logger.info("坏导通道已修复")

            self._csp_filters = []

            # 对每个频段训练CSP滤波器
            for i, (low, high) in enumerate(self.freq_bands):
                logger.debug(f"训练频段 {i+1}/{len(self.freq_bands)}: {low}-{high}Hz")
                
                # 带通滤波
                filtered_data = self._apply_bandpass_filter(X, low, high)

                # 创建并训练CSP滤波器
                csp = CSP(
                    n_components=self.n_components,
                    reg=self.reg,
                    log=False,  # 我们手动处理对数变换
                    norm_trace=False
                )

                csp.fit(filtered_data, y)
                self._csp_filters.append(csp)

                logger.debug(f"频段 {low}-{high}Hz CSP训练完成")

            logger.info(f"FBCSP训练完成，共{len(self._csp_filters)}个频段")

            return self
            
        except Exception as e:
            logger.error(f"FBCSP训练失败: {e}")
            raise
    
    def _transform(self, X: np.ndarray) -> np.ndarray:
        """
        变换数据为FBCSP特征
        
        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]
            
        Returns:
            FBCSP特征 [n_trials, n_features]
        """
        try:
            if not self._csp_filters:
                raise ValueError("CSP滤波器未训练，请先调用fit方法")

            all_features = []

            # 对每个频段提取特征
            for i, ((low, high), csp_filter) in enumerate(zip(self.freq_bands, self._csp_filters)):
                # 带通滤波
                filtered_data = self._apply_bandpass_filter(X, low, high)

                # 提取CSP特征
                features = self._extract_csp_features(filtered_data, csp_filter)
                all_features.append(features)

                logger.debug(f"频段 {low}-{high}Hz 特征提取完成，形状: {features.shape}")

            # 拼接所有频段的特征
            fbcsp_features = np.concatenate(all_features, axis=1)
            
            logger.debug(f"FBCSP特征提取完成，总特征维度: {fbcsp_features.shape[1]}")
            
            return fbcsp_features
            
        except Exception as e:
            logger.error(f"FBCSP特征变换失败: {e}")
            raise
    
    def get_feature_names(self) -> List[str]:
        """
        获取特征名称列表
        
        Returns:
            特征名称列表
        """
        feature_names = []

        for i, (low, high) in enumerate(self.freq_bands):
            for j in range(self.n_components):
                feature_names.append(f"fbcsp_band{i+1}_{low}-{high}Hz_comp{j+1}")

        return feature_names
    
    def get_csp_patterns(self, band_idx: int) -> np.ndarray:
        """
        获取指定频段的CSP模式
        
        Args:
            band_idx: 频段索引
            
        Returns:
            CSP模式 [n_components, n_channels]
        """
        if not self._is_fitted:
            raise ValueError("模型尚未拟合")
        
        if band_idx < 0 or band_idx >= len(self._csp_filters):
            raise ValueError(f"频段索引超出范围: {band_idx}")
        
        return self._csp_filters[band_idx].patterns_
    
    def get_csp_filters_weights(self, band_idx: int) -> np.ndarray:
        """
        获取指定频段的CSP滤波器权重
        
        Args:
            band_idx: 频段索引
            
        Returns:
            CSP滤波器权重 [n_components, n_channels]
        """
        if not self._is_fitted:
            raise ValueError("模型尚未拟合")
        
        if band_idx < 0 or band_idx >= len(self._csp_filters):
            raise ValueError(f"频段索引超出范围: {band_idx}")
        
        return self._csp_filters[band_idx].filters_
    
    def plot_csp_patterns(self, band_idx: int, info: Optional[object] = None):
        """
        绘制CSP模式地形图
        
        Args:
            band_idx: 频段索引
            info: MNE Info对象，包含通道位置信息
        """
        if not self._is_fitted:
            raise ValueError("模型尚未拟合")
        
        if band_idx < 0 or band_idx >= len(self._csp_filters):
            raise ValueError(f"频段索引超出范围: {band_idx}")
        
        try:
            csp_filter = self._csp_filters[band_idx]
            low, high = self.freq_bands[band_idx]
            
            if info is not None:
                csp_filter.plot_patterns(
                    info, 
                    ch_type='eeg',
                    units='Patterns (AU)',
                    size=1.5,
                    show=True
                )
            else:
                logger.warning("需要MNE Info对象来绘制地形图")
                
        except Exception as e:
            logger.error(f"绘制CSP模式失败: {e}")
            raise
    
    def get_band_powers(self, X: np.ndarray) -> np.ndarray:
        """
        获取各频段的功率
        
        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]
            
        Returns:
            频段功率 [n_trials, n_bands, n_channels]
        """
        try:
            band_powers = []
            
            for low, high in self.freq_bands:
                # 带通滤波
                filtered_data = self._apply_bandpass_filter(X, low, high)
                
                # 计算功率（方差）
                power = np.var(filtered_data, axis=2)
                band_powers.append(power)
            
            return np.array(band_powers).transpose(1, 0, 2)  # [n_trials, n_bands, n_channels]
            
        except Exception as e:
            logger.error(f"频段功率计算失败: {e}")
            raise
    
    def get_params(self, deep: bool = True) -> dict:
        """获取参数"""
        params = super().get_params(deep)
        params.update({
            'freq_bands': self.freq_bands,
            'n_components': self.n_components,
            'reg': self.reg,
            'log': self.log
        })
        return params
    
    def set_params(self, **params) -> 'FBCSPExtractor':
        """设置参数"""
        # 如果修改了关键参数，需要重新训练
        critical_params = ['freq_bands', 'n_components', 'reg']
        need_refit = any(param in critical_params for param in params.keys())
        
        if need_refit and self._is_fitted:
            logger.warning("修改了关键参数，模型需要重新训练")
            self._is_fitted = False
            self._csp_filters = []
        
        return super().set_params(**params)
    
    def __repr__(self) -> str:
        """字符串表示"""
        return (f"FBCSPExtractor("
                f"n_bands={len(self.freq_bands)}, "
                f"n_components={self.n_components}, "
                f"sampling_rate={self.sampling_rate}, "
                f"reg={self.reg}, "
                f"log={self.log}, "
                f"fitted={self._is_fitted})")
