{"sampling_rate": 125, "n_channels": 8, "n_samples": 250, "fbcsp": {"enabled": true, "freq_bands": [[8, 13], [13, 30]], "n_components": 3, "reg": "oas", "log": true}, "riemannian": {"enabled": true, "metric": "<PERSON><PERSON><PERSON>", "estimator": "oas", "tangent_space": true, "reference_method": "mean", "regularization": 0.1}, "tef": {"enabled": true, "time_features": {"mean": true, "std": true, "var": false, "skew": false, "kurtosis": false, "rms": true, "peak": false, "energy": false, "peak_to_peak": false}, "entropy_features": {"sample_entropy": false, "approximate_entropy": false, "permutation_entropy": false, "fuzzy_entropy": false, "multiscale_entropy": false}, "freq_features": {"power_spectral_density": true, "spectral_centroid": false, "spectral_bandwidth": false, "spectral_rolloff": false, "spectral_flatness": false, "spectral_crest": false, "dominant_frequency": true, "frequency_variance": false}, "freq_bands": [[8, 13], [13, 30]], "nperseg": 125, "feature_selection": {"enabled": true, "method": "mrmr", "max_features": 8, "selected_channels": [3, 4, 5]}}, "tangent_space": {"enabled": true, "estimator": "cov", "metric": "<PERSON><PERSON><PERSON>", "regularization": 0.001}, "plv": {"enabled": true, "freq_bands": [[8, 13], [13, 30]], "channel_pairs": [[4, 6], [1, 3], [7, 8]], "filter_order": 6, "method": "hilbert", "window_length": 250, "overlap": 0.0, "normalize": true, "_comment_freq_bands": "🔧 优化：α波和β波频段，减少特征维度", "_comment_channel_pairs": "🔧 优化：专注运动皮层通道对[C3,CZ,C4]", "_comment_pairs_detail": "🔧 C3-C4(左右运动皮层), CZ-C3(中央-左运动), CZ-C4(中央-右运动)，2频段×3通道对=6个特征", "_comment_optimization": "🔧 窗口1秒+50%重叠提高敏感性，滤波器阶数降低减少延迟"}, "fusion": {"method": "concatenate", "normalize": false, "feature_selection": true, "selection_method": "variance", "n_features": 50, "pca_components": null, "pca_variance_ratio": 0.95}, "enable_caching": true, "cache_dir": "data/cache/features", "max_cache_size": 1000, "log_level": "INFO", "log_performance": true}