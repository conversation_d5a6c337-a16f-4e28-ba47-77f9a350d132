"""
8-30Hz频段优化配置测试脚本

测试针对8-30Hz带通滤波器优化的特征提取配置
包括FBCSP、TEF、Riemannian、TangentSpace、PLV的参数优化
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import json
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from services.validation.test_5_algorithms import FiveAlgorithmsValidator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('validation_optimized_8_30Hz.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class Optimized8_30HzValidator:
    """8-30Hz频段优化配置验证器"""
    
    def __init__(self, dataset_path: str):
        """
        初始化8-30Hz优化验证器
        
        Args:
            dataset_path: EEG数据集路径
        """
        self.dataset_path = dataset_path
        
        # 测试前3名受试者（快速验证）
        self.test_subjects = [f"S{i:03d}" for i in range(1, 4)]
        
        logger.info(f"8-30Hz频段优化验证器初始化完成")
        logger.info(f"数据集路径: {dataset_path}")
        logger.info(f"测试受试者: {self.test_subjects}")
        logger.info(f"优化内容: FBCSP+TEF+Riemannian+TangentSpace+PLV参数优化")
    
    def run_optimized_test(self, cv_folds: int = 3) -> Dict[str, Any]:
        """
        运行优化配置测试
        
        Args:
            cv_folds: 交叉验证折数
            
        Returns:
            优化测试结果
        """
        logger.info("开始8-30Hz频段优化配置测试")
        logger.info("优化内容:")
        logger.info("  • FBCSP: 扩展到3个频段[8-12, 13-20, 21-30Hz]，增加组件数")
        logger.info("  • TEF: 扩展频段，增加spectral_bandwidth和frequency_variance")
        logger.info("  • Riemannian: 改用oas估计器，降低正则化")
        logger.info("  • TangentSpace: 改用oas估计器，大幅降低正则化")
        logger.info("  • PLV: 扩展到3个频段，增加通道对数量")
        logger.info(f"测试受试者数量: {len(self.test_subjects)}")
        logger.info(f"交叉验证折数: {cv_folds}")
        
        # 测试优化后的配置
        logger.info("=" * 60)
        logger.info("测试优化后的8-30Hz配置...")
        logger.info("=" * 60)
        
        validator_optimized = FiveAlgorithmsValidator(
            self.dataset_path,
            results_dir="validation_results_optimized_8_30Hz",
            enable_preprocessing=True
        )
        
        # 使用优化后的配置
        validator_optimized.validator.feature_manager.config = self._load_optimized_config()
        
        results_optimized = self._run_limited_validation(
            validator_optimized, cv_folds
        )
        
        # 生成优化报告
        optimization_report = self._generate_optimization_report(results_optimized)
        
        # 保存结果
        self._save_optimization_results(optimization_report)
        
        logger.info("8-30Hz频段优化配置测试完成")
        return optimization_report
    
    def _load_optimized_config(self):
        """加载优化后的配置"""
        try:
            from services.feature_extraction.config import FeatureExtractionConfig
            from utils.path_manager import get_config_file_in_dir
            config_path = get_config_file_in_dir('feature_extraction_optimized_8_30Hz.json')
            config = FeatureExtractionConfig.from_file(str(config_path))
            logger.info("已加载优化后的8-30Hz配置")
            return config
        except Exception as e:
            logger.warning(f"加载优化配置失败: {e}，使用默认配置")
            from services.feature_extraction.config import load_config
            fallback_path = get_config_file_in_dir('feature_extraction_optimized.json')
            return load_config(str(fallback_path))
    
    def _run_limited_validation(self, validator: FiveAlgorithmsValidator, cv_folds: int) -> Dict[str, Any]:
        """运行限制受试者的验证"""
        # 检查数据集可用性
        available_subjects = []
        
        for subject_id in self.test_subjects:
            subject_path = os.path.join(self.dataset_path, subject_id)
            if os.path.exists(subject_path):
                # 检查必需的文件
                required_files = [
                    f"{subject_id}R06.edf",
                    f"{subject_id}R06.edf.event", 
                    f"{subject_id}R10.edf",
                    f"{subject_id}R10.edf.event"
                ]
                
                files_exist = all(
                    os.path.exists(os.path.join(subject_path, file)) 
                    for file in required_files
                )
                
                if files_exist:
                    available_subjects.append(subject_id)
                    logger.info(f"✓ {subject_id}: 数据文件完整")
                else:
                    logger.warning(f"✗ {subject_id}: 缺少必需文件")
            else:
                logger.warning(f"✗ {subject_id}: 目录不存在")
        
        if not available_subjects:
            raise ValueError("没有找到可用的受试者数据")
        
        # 执行验证
        results = validator.validator.validate_multiple_subjects(available_subjects, cv_folds)
        return results
    
    def _generate_optimization_report(self, results_optimized: Dict[str, Any]) -> Dict[str, Any]:
        """生成优化报告"""
        logger.info("生成8-30Hz优化配置报告...")
        
        # 算法名称映射
        algorithm_names = {
            'fbcsp_svm': 'FBCSP + SVM',
            'tef_rf': 'TEF + RandomForest',
            'riemannian_meanfield': 'Riemannian + MeanField',
            'tangent_space_lr': 'TangentSpace + LogisticRegression',
            'plv_svm': 'PLV + SVM'
        }
        
        optimization_report = {
            'test_info': {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'dataset_path': self.dataset_path,
                'tested_subjects': self.test_subjects,
                'total_subjects': len(self.test_subjects),
                'optimization_type': '8-30Hz频段特征提取参数优化',
                'config_file': 'feature_extraction_optimized_8_30Hz.json'
            },
            'optimized_results': results_optimized,
            'algorithm_performance': {},
            'optimization_details': {
                'fbcsp': '扩展到3个频段[8-12, 13-20, 21-30Hz]，增加组件数到4',
                'tef': '扩展频段，增加spectral_bandwidth和frequency_variance特征',
                'riemannian': '改用oas估计器，降低正则化到0.05',
                'tangent_space': '改用oas估计器，大幅降低正则化到0.001',
                'plv': '扩展到3个频段，增加通道对到6个，特征数18个'
            }
        }
        
        # 算法性能分析
        if 'algorithms' in results_optimized:
            for alg_key, alg_name in algorithm_names.items():
                if alg_key in results_optimized['algorithms']:
                    alg_data = results_optimized['algorithms'][alg_key]
                    
                    optimization_report['algorithm_performance'][alg_name] = {
                        'accuracy': alg_data.get('mean_cv_accuracy', 0),
                        'std': alg_data.get('std_cv_accuracy', 0),
                        'subjects_tested': alg_data.get('n_subjects', 0),
                        'algorithm_key': alg_key,
                        'optimization_applied': optimization_report['optimization_details'].get(alg_key.split('_')[0], '未知')
                    }
        
        return optimization_report
    
    def _save_optimization_results(self, optimization_report: Dict[str, Any]):
        """保存优化结果"""
        # 保存详细报告
        report_file = "optimized_8_30Hz_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(optimization_report, f, indent=2, ensure_ascii=False)
        
        # 生成简化的文本报告
        self._save_text_summary(optimization_report)
        
        logger.info(f"8-30Hz优化配置报告已保存: {report_file}")
    
    def _save_text_summary(self, optimization_report: Dict[str, Any]):
        """保存文本摘要"""
        try:
            summary_lines = []
            
            # 报告头部
            summary_lines.append("8-30Hz频段优化配置测试报告")
            summary_lines.append("=" * 50)
            summary_lines.append(f"生成时间: {optimization_report['test_info']['timestamp']}")
            summary_lines.append(f"测试受试者: {len(optimization_report['test_info']['tested_subjects'])}名")
            summary_lines.append(f"配置文件: {optimization_report['test_info']['config_file']}")
            summary_lines.append("")
            
            # 优化详情
            summary_lines.append("优化详情")
            summary_lines.append("-" * 30)
            for alg, detail in optimization_report['optimization_details'].items():
                summary_lines.append(f"{alg.upper()}: {detail}")
            summary_lines.append("")
            
            # 算法性能表格
            if 'algorithm_performance' in optimization_report:
                summary_lines.append("算法性能结果 (优化后)")
                summary_lines.append("-" * 60)
                summary_lines.append(f"{'算法名称':<30} {'准确率':<12} {'标准差':<12}")
                summary_lines.append("-" * 60)
                
                for alg_name, data in optimization_report['algorithm_performance'].items():
                    accuracy = f"{data['accuracy']:.3f}"
                    std = f"±{data['std']:.3f}"
                    
                    summary_lines.append(f"{alg_name:<30} {accuracy:<12} {std:<12}")
                
                summary_lines.append("-" * 60)
            
            # 保存文本摘要
            summary_file = "optimized_8_30Hz_summary.txt"
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("\n".join(summary_lines))
            
            logger.info(f"文本摘要已保存: {summary_file}")
            
        except Exception as e:
            logger.warning(f"保存文本摘要失败: {e}")


def main():
    """主函数"""
    # 数据集路径
    dataset_path = r"D:\脑电\数据\EEG Motor MovementImagery Dataset"
    
    # 检查数据集路径
    if not os.path.exists(dataset_path):
        logger.error(f"数据集路径不存在: {dataset_path}")
        return
    
    try:
        # 创建验证器
        validator = Optimized8_30HzValidator(dataset_path)
        
        # 执行优化测试
        results = validator.run_optimized_test(cv_folds=3)
        
        # 打印简要结果
        print("\n" + "="*60)
        print("8-30Hz频段优化配置测试结果摘要")
        print("="*60)
        
        if 'algorithm_performance' in results:
            print("\n算法性能结果 (优化后):")
            for alg_name, data in results['algorithm_performance'].items():
                accuracy = data['accuracy']
                std = data['std']
                print(f"{alg_name}: {accuracy:.3f}±{std:.3f}")
        
        print("\n详细结果已保存到当前目录")
        print("="*60)
        
    except Exception as e:
        logger.error(f"8-30Hz优化配置测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
