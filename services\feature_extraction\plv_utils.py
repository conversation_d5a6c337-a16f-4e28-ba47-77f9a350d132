#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PLV (Phase Locking Value) 计算工具函数

实现相位锁定值的核心计算功能，专门针对8通道脑电信号的运动想象分类
支持多频段PLV计算，优化实时性能

作者: AI Assistant  
创建时间: 2025-01-11
"""

import numpy as np
import logging
from scipy import signal
from typing import List, Tuple, Dict, Optional
import time

logger = logging.getLogger(__name__)

class PLVCalculator:
    """
    PLV计算器
    
    实现高效的相位锁定值计算，支持多频段和多通道对
    """
    
    def __init__(self, 
                 sampling_rate: int = 125,
                 freq_bands: List[Tuple[float, float]] = None,
                 channel_pairs: List[Tuple[int, int]] = None,
                 filter_order: int = 4):
        """
        初始化PLV计算器
        
        Args:
            sampling_rate: 采样率
            freq_bands: 频段列表 [(low, high), ...]
            channel_pairs: 通道对列表 [(ch1, ch2), ...]
            filter_order: 滤波器阶数
        """
        self.sampling_rate = sampling_rate
        self.filter_order = filter_order
        
        # 默认频段：α波(8-13Hz)和β波(13-30Hz)
        if freq_bands is None:
            self.freq_bands = [(8, 13), (13, 30)]
        else:
            self.freq_bands = freq_bands
            
        # 默认通道对：基于8通道电极布局 [PZ, P3, P4, C3, CZ, C4, F3, F4]
        if channel_pairs is None:
            self.channel_pairs = [
                (3, 5),  # C3-C4 (通道4-6，索引3-5)
                (4, 3),  # CZ-C3 (通道5-4，索引4-3)  
                (4, 5),  # CZ-C4 (通道5-6，索引4-5)
                (3, 1),  # C3-P3 (通道4-2，索引3-1)
                (5, 2),  # C4-P4 (通道6-3，索引5-2)
                (4, 0),  # CZ-PZ (通道5-1，索引4-0)
                (6, 3),  # F3-C3 (通道7-4，索引6-3)
                (7, 5),  # F4-C4 (通道8-6，索引7-5)
            ]
        else:
            # 转换为0-based索引
            self.channel_pairs = [(ch1-1, ch2-1) for ch1, ch2 in channel_pairs]
        
        # 预计算滤波器
        self.filters = self._create_bandpass_filters()
        
        logger.info(f"PLV计算器初始化完成")
        logger.info(f"频段: {self.freq_bands}")
        logger.info(f"通道对数量: {len(self.channel_pairs)}")
        
    def _create_bandpass_filters(self) -> List[Tuple[np.ndarray, np.ndarray]]:
        """
        预计算所有频段的带通滤波器
        
        Returns:
            滤波器系数列表
        """
        filters = []
        for low_freq, high_freq in self.freq_bands:
            # 设计Butterworth带通滤波器
            nyquist = self.sampling_rate / 2
            low_norm = low_freq / nyquist
            high_norm = high_freq / nyquist
            
            # 确保频率在有效范围内
            low_norm = max(0.01, min(0.99, low_norm))
            high_norm = max(0.01, min(0.99, high_norm))
            
            if low_norm >= high_norm:
                high_norm = low_norm + 0.1
                
            b, a = signal.butter(self.filter_order, [low_norm, high_norm], 
                               btype='band', analog=False)
            filters.append((b, a))
            
        logger.debug(f"创建了{len(filters)}个带通滤波器")
        return filters
    
    def _apply_bandpass_filter(self, data: np.ndarray, filter_coeffs: Tuple[np.ndarray, np.ndarray]) -> np.ndarray:
        """
        应用带通滤波器
        
        Args:
            data: 输入数据 [n_channels, n_samples]
            filter_coeffs: 滤波器系数 (b, a)
            
        Returns:
            滤波后的数据
        """
        b, a = filter_coeffs
        filtered_data = np.zeros_like(data)
        
        for ch in range(data.shape[0]):
            filtered_data[ch] = signal.filtfilt(b, a, data[ch])
            
        return filtered_data
    
    def _extract_instantaneous_phase(self, signal_data: np.ndarray) -> np.ndarray:
        """
        使用Hilbert变换提取瞬时相位
        
        Args:
            signal_data: 输入信号 [n_samples]
            
        Returns:
            瞬时相位 [n_samples]
        """
        # Hilbert变换
        analytic_signal = signal.hilbert(signal_data)
        
        # 提取瞬时相位
        instantaneous_phase = np.angle(analytic_signal)
        
        return instantaneous_phase
    
    def _calculate_plv_single_pair(self, phase1: np.ndarray, phase2: np.ndarray) -> float:
        """
        计算单个通道对的PLV值
        
        Args:
            phase1: 通道1的瞬时相位
            phase2: 通道2的瞬时相位
            
        Returns:
            PLV值 (0-1之间)
        """
        # 计算相位差
        phase_diff = phase1 - phase2
        
        # 计算PLV：相位差复数表示的平均值的模长
        plv = np.abs(np.mean(np.exp(1j * phase_diff)))
        
        return plv
    
    def calculate_plv_features(self, eeg_data: np.ndarray) -> np.ndarray:
        """
        计算完整的PLV特征向量
        
        Args:
            eeg_data: EEG数据 [n_channels, n_samples]
            
        Returns:
            PLV特征向量 [n_freq_bands * n_channel_pairs]
        """
        try:
            start_time = time.perf_counter()
            
            if eeg_data.shape[0] != 8:
                raise ValueError(f"期望8个通道，实际得到{eeg_data.shape[0]}个通道")
            
            plv_features = []
            
            # 对每个频段计算PLV
            for freq_idx, filter_coeffs in enumerate(self.filters):
                # 带通滤波
                filtered_data = self._apply_bandpass_filter(eeg_data, filter_coeffs)
                
                # 提取所有通道的瞬时相位
                phases = np.zeros((8, eeg_data.shape[1]))
                for ch in range(8):
                    phases[ch] = self._extract_instantaneous_phase(filtered_data[ch])
                
                # 计算所有通道对的PLV
                freq_plv_values = []
                for ch1, ch2 in self.channel_pairs:
                    plv_value = self._calculate_plv_single_pair(phases[ch1], phases[ch2])
                    freq_plv_values.append(plv_value)
                
                plv_features.extend(freq_plv_values)
                
                logger.debug(f"频段{freq_idx+1} PLV计算完成，平均值: {np.mean(freq_plv_values):.3f}")
            
            plv_features = np.array(plv_features)
            
            # 性能统计
            processing_time = (time.perf_counter() - start_time) * 1000
            logger.debug(f"PLV特征计算完成，耗时: {processing_time:.2f}ms，特征维度: {len(plv_features)}")
            
            return plv_features
            
        except Exception as e:
            logger.error(f"PLV特征计算失败: {e}")
            # 返回零特征向量作为备选
            n_features = len(self.freq_bands) * len(self.channel_pairs)
            return np.zeros(n_features)
    
    def get_feature_names(self) -> List[str]:
        """
        获取特征名称列表
        
        Returns:
            特征名称列表
        """
        feature_names = []
        
        # 通道名称映射
        channel_names = ['PZ', 'P3', 'P4', 'C3', 'CZ', 'C4', 'F3', 'F4']
        
        for freq_idx, (low_freq, high_freq) in enumerate(self.freq_bands):
            freq_name = f"{low_freq}-{high_freq}Hz"
            
            for ch1, ch2 in self.channel_pairs:
                pair_name = f"{channel_names[ch1]}-{channel_names[ch2]}"
                feature_name = f"PLV_{freq_name}_{pair_name}"
                feature_names.append(feature_name)
        
        return feature_names
    
    def validate_configuration(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            配置是否有效
        """
        try:
            # 检查频段
            for low_freq, high_freq in self.freq_bands:
                if low_freq >= high_freq:
                    logger.error(f"无效频段: {low_freq}-{high_freq}Hz")
                    return False
                if high_freq > self.sampling_rate / 2:
                    logger.error(f"频段超出奈奎斯特频率: {high_freq}Hz > {self.sampling_rate/2}Hz")
                    return False
            
            # 检查通道对
            for ch1, ch2 in self.channel_pairs:
                if ch1 < 0 or ch1 >= 8 or ch2 < 0 or ch2 >= 8:
                    logger.error(f"无效通道对: {ch1}-{ch2}")
                    return False
                if ch1 == ch2:
                    logger.warning(f"通道对相同: {ch1}-{ch2}")
            
            logger.info("PLV配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"PLV配置验证失败: {e}")
            return False


def create_default_plv_calculator() -> PLVCalculator:
    """
    创建默认的PLV计算器
    
    Returns:
        配置好的PLV计算器实例
    """
    return PLVCalculator(
        sampling_rate=125,
        freq_bands=[(8, 13), (13, 30)],  # α波和β波
        channel_pairs=None,  # 使用默认通道对
        filter_order=4
    )


def test_plv_calculation():
    """
    测试PLV计算功能
    """
    print("测试PLV计算功能...")
    
    # 创建测试数据
    n_channels = 8
    n_samples = 250  # 2秒数据
    test_data = np.random.randn(n_channels, n_samples)
    
    # 创建PLV计算器
    plv_calc = create_default_plv_calculator()
    
    # 验证配置
    if not plv_calc.validate_configuration():
        print("❌ PLV配置验证失败")
        return False
    
    # 计算PLV特征
    plv_features = plv_calc.calculate_plv_features(test_data)
    
    # 获取特征名称
    feature_names = plv_calc.get_feature_names()
    
    print(f"✅ PLV特征计算成功")
    print(f"特征维度: {len(plv_features)}")
    print(f"特征范围: [{np.min(plv_features):.3f}, {np.max(plv_features):.3f}]")
    print(f"特征名称示例: {feature_names[:3]}")
    
    return True


if __name__ == "__main__":
    test_plv_calculation()
