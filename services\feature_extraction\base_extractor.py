"""
基础特征提取器抽象类

定义所有特征提取器的统一接口和通用功能。
"""

import numpy as np
import pickle
import os
import time
import logging
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, Tuple
from sklearn.base import BaseEstimator, TransformerMixin

logger = logging.getLogger(__name__)

class BaseFeatureExtractor(ABC, BaseEstimator, TransformerMixin):
    """特征提取器基类
    
    所有特征提取器都应继承此类并实现抽象方法。
    提供统一的接口和通用功能如缓存、验证、性能监控等。
    """
    
    def __init__(self,
                 sampling_rate: int = 125,
                 n_channels: int = 8,
                 n_samples: int = None,  # 自动适应样本数，不再硬编码
                 enable_caching: bool = True,
                 cache_dir: str = None):
        """
        初始化基础特征提取器

        Args:
            sampling_rate: 采样率 (Hz)
            n_channels: 通道数
            n_samples: 每个试次的样本数（None表示自动适应）
            enable_caching: 是否启用缓存
            cache_dir: 缓存目录
        """
        self.sampling_rate = sampling_rate
        self.n_channels = n_channels
        self.n_samples = n_samples  # None表示自动适应
        self.enable_caching = enable_caching

        # 设置缓存目录
        if cache_dir is None:
            from utils.path_manager import get_data_file
            self.cache_dir = str(get_data_file("cache/features"))
        else:
            self.cache_dir = cache_dir
        
        # 内部状态
        self._is_fitted = False
        self._feature_names = None
        self._performance_stats = {
            'fit_time': 0.0,
            'transform_time': 0.0,
            'n_fit_calls': 0,
            'n_transform_calls': 0
        }
        
        # 创建缓存目录
        if self.enable_caching:
            os.makedirs(self.cache_dir, exist_ok=True)
    
    @abstractmethod
    def _fit(self, X: np.ndarray, y: np.ndarray) -> 'BaseFeatureExtractor':
        """
        实际的拟合逻辑，由子类实现
        
        Args:
            X: 训练数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]
            
        Returns:
            self
        """
        pass
    
    @abstractmethod
    def _transform(self, X: np.ndarray) -> np.ndarray:
        """
        实际的变换逻辑，由子类实现
        
        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]
            
        Returns:
            特征矩阵 [n_trials, n_features]
        """
        pass
    
    @abstractmethod
    def get_feature_names(self) -> list:
        """
        获取特征名称列表，由子类实现
        
        Returns:
            特征名称列表
        """
        pass
    
    def validate_input(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> bool:
        """
        验证输入数据格式
        
        Args:
            X: 输入数据
            y: 标签（可选）
            
        Returns:
            验证是否通过
        """
        try:
            # 检查X的形状
            if X.ndim != 3:
                logger.error(f"输入数据应为3维，实际为{X.ndim}维")
                return False
            
            n_trials, n_channels, n_samples = X.shape
            
            if n_channels != self.n_channels:
                logger.error(f"通道数不匹配，期望{self.n_channels}，实际{n_channels}")
                return False
            
            # 自动适应样本数或验证固定样本数
            if self.n_samples is not None and n_samples != self.n_samples:
                logger.error(f"样本数不匹配，期望{self.n_samples}，实际{n_samples}")
                return False
            elif self.n_samples is None:
                # 首次拟合时自动设置样本数
                if not hasattr(self, '_fitted_n_samples'):
                    self._fitted_n_samples = n_samples
                    logger.info(f"自动适应样本数: {n_samples}")
                # 后续验证样本数一致性
                elif n_samples != self._fitted_n_samples:
                    logger.error(f"样本数不一致，期望{self._fitted_n_samples}，实际{n_samples}")
                    return False
            
            # 检查数据类型
            if not np.issubdtype(X.dtype, np.floating):
                logger.warning("输入数据不是浮点型，将自动转换")
            
            # 检查是否有无效值
            if np.any(np.isnan(X)) or np.any(np.isinf(X)):
                logger.warning("输入数据包含NaN或Inf值，尝试清理...")

                # 详细统计无效值
                nan_count = np.sum(np.isnan(X))
                inf_count = np.sum(np.isinf(X))
                total_elements = X.size
                invalid_ratio = (nan_count + inf_count) / total_elements

                logger.warning(f"无效值统计: NaN={nan_count}, Inf={inf_count}, 比例={invalid_ratio:.4f}")

                # 分析每个通道的无效值情况
                self._analyze_channel_quality(X)

                # 如果无效值比例过高，尝试通道级别的处理
                if invalid_ratio > 0.1:  # 超过10%的数据无效
                    logger.warning(f"无效值比例过高({invalid_ratio:.2%})，尝试通道级别处理...")

                    # 检查是否可以通过通道级别处理来挽救数据
                    if self._can_salvage_data(X):
                        logger.info("检测到部分通道可用，将尝试通道级别的数据修复")
                    else:
                        logger.error(f"无效值比例过高({invalid_ratio:.2%})，且无法通过通道级别处理修复，拒绝处理")
                        return False

                # 尝试高级数据清理
                try:
                    # 使用高级清理方法进行测试
                    X_test_cleaned = self._clean_data_advanced(X)

                    # 检查清理后的数据
                    if np.any(np.isnan(X_test_cleaned)) or np.any(np.isinf(X_test_cleaned)):
                        logger.error("高级数据清理失败，仍包含无效值")
                        return False

                    logger.info("高级数据清理测试成功，数据可以修复")
                    # 注意：这里不能直接修改X，因为它是传入的参数
                    # 我们在fit方法中进行实际的清理

                except Exception as e:
                    logger.error(f"高级数据清理失败: {e}")
                    return False
            
            # 检查标签（如果提供）
            if y is not None:
                if len(y) != n_trials:
                    logger.error(f"标签数量不匹配，期望{n_trials}，实际{len(y)}")
                    return False
                
                if not np.all(np.isin(y, [0, 1])):
                    logger.error("标签应为0或1")
                    return False
            
            return True

        except Exception as e:
            logger.error(f"输入验证失败: {e}")
            return False

    def _clean_data(self, X: np.ndarray) -> np.ndarray:
        """
        清理数据中的NaN和Inf值

        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]

        Returns:
            清理后的数据
        """
        try:
            # 检查是否需要清理
            if not (np.any(np.isnan(X)) or np.any(np.isinf(X))):
                return X

            logger.info("开始清理数据中的NaN/Inf值...")

            X_cleaned = np.copy(X)

            # 统计无效值
            nan_mask = np.isnan(X_cleaned)
            inf_mask = np.isinf(X_cleaned)

            nan_count = np.sum(nan_mask)
            inf_count = np.sum(inf_mask)

            if nan_count > 0:
                logger.warning(f"发现{nan_count}个NaN值，将替换为0")
                X_cleaned[nan_mask] = 0.0

            if inf_count > 0:
                logger.warning(f"发现{inf_count}个Inf值，将替换为0")
                X_cleaned[inf_mask] = 0.0

            # 验证清理结果
            if np.any(np.isnan(X_cleaned)) or np.any(np.isinf(X_cleaned)):
                logger.error("数据清理失败，仍包含无效值")
                raise ValueError("数据清理失败")

            logger.info("数据清理完成")
            return X_cleaned

        except Exception as e:
            logger.error(f"数据清理过程失败: {e}")
            raise

    def _clean_data_advanced(self, X: np.ndarray) -> np.ndarray:
        """
        高级数据清理，支持通道级别的智能处理

        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]

        Returns:
            清理后的数据
        """
        try:
            # 检查是否需要清理
            if not (np.any(np.isnan(X)) or np.any(np.isinf(X))):
                return X

            logger.info("开始智能清理数据中的NaN/Inf值...")

            X_cleaned = np.copy(X)

            if X_cleaned.ndim == 2:
                X_cleaned = X_cleaned[np.newaxis, ...]
                was_2d = True
            else:
                was_2d = False

            n_trials, n_channels, n_samples = X_cleaned.shape

            # 统计无效值
            nan_mask = np.isnan(X_cleaned)
            inf_mask = np.isinf(X_cleaned)
            invalid_mask = nan_mask | inf_mask

            nan_count = np.sum(nan_mask)
            inf_count = np.sum(inf_mask)

            logger.info(f"发现无效值: NaN={nan_count}, Inf={inf_count}")

            # 通道级别的智能处理
            for ch in range(n_channels):
                channel_invalid_mask = invalid_mask[:, ch, :]
                channel_invalid_ratio = np.sum(channel_invalid_mask) / channel_invalid_mask.size

                if channel_invalid_ratio > 0:
                    if channel_invalid_ratio < 0.3:  # 轻微损坏：插值修复
                        logger.info(f"通道{ch+1}: 轻微损坏({channel_invalid_ratio:.1%})，使用插值修复")
                        X_cleaned[:, ch, :] = self._interpolate_channel(X_cleaned[:, ch, :], channel_invalid_mask)
                    elif channel_invalid_ratio < 0.7:  # 中度损坏：邻近通道平均
                        logger.warning(f"通道{ch+1}: 中度损坏({channel_invalid_ratio:.1%})，使用邻近通道平均")
                        X_cleaned[:, ch, :] = self._replace_with_neighbors(X_cleaned, ch, channel_invalid_mask)
                    else:  # 严重损坏：优先使用邻近通道，避免置零
                        logger.error(f"通道{ch+1}: 严重损坏({channel_invalid_ratio:.1%})，尝试邻近通道修复")
                        repaired_data = self._replace_with_neighbors(X_cleaned, ch, channel_invalid_mask)

                        # 检查修复效果
                        if np.any(np.isnan(repaired_data)) or np.any(np.isinf(repaired_data)):
                            logger.warning(f"通道{ch+1}: 邻近通道修复失败，使用低幅度噪声替代")
                            # 使用低幅度随机噪声而不是零值，保持信号特性
                            noise_amplitude = 0.1  # 微伏级别的噪声
                            X_cleaned[:, ch, :] = np.random.normal(0, noise_amplitude, X_cleaned[:, ch, :].shape)
                        else:
                            X_cleaned[:, ch, :] = repaired_data

            # 验证清理结果
            if np.any(np.isnan(X_cleaned)) or np.any(np.isinf(X_cleaned)):
                logger.error("数据清理失败，仍包含无效值")
                raise ValueError("数据清理失败")

            logger.info("智能数据清理完成")

            # 恢复原始维度
            if was_2d:
                X_cleaned = X_cleaned[0]

            return X_cleaned

        except Exception as e:
            logger.error(f"高级数据清理失败: {e}")
            raise

    def _analyze_channel_quality(self, X: np.ndarray):
        """
        分析每个通道的数据质量

        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]
        """
        try:
            if X.ndim == 2:
                X = X[np.newaxis, ...]  # 添加试次维度

            n_trials, n_channels, n_samples = X.shape

            logger.info("=== 通道质量分析 ===")

            for ch in range(n_channels):
                channel_data = X[:, ch, :]

                # 统计该通道的无效值
                nan_count = np.sum(np.isnan(channel_data))
                inf_count = np.sum(np.isinf(channel_data))
                total_elements = channel_data.size
                invalid_ratio = (nan_count + inf_count) / total_elements

                # 计算有效数据的统计信息
                valid_data = channel_data[~(np.isnan(channel_data) | np.isinf(channel_data))]

                if len(valid_data) > 0:
                    mean_val = np.mean(valid_data)
                    std_val = np.std(valid_data)
                    range_val = np.ptp(valid_data)

                    status = "正常" if invalid_ratio < 0.05 else "异常" if invalid_ratio < 0.5 else "严重异常"

                    logger.info(f"通道{ch+1}: {status} | 无效值比例={invalid_ratio:.2%} | "
                              f"均值={mean_val:.2f} | 标准差={std_val:.2f} | 范围={range_val:.2f}")
                else:
                    logger.error(f"通道{ch+1}: 完全无效 | 无效值比例=100%")

        except Exception as e:
            logger.error(f"通道质量分析失败: {e}")

    def _can_salvage_data(self, X: np.ndarray) -> bool:
        """
        检查是否可以通过通道级别处理来挽救数据

        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]

        Returns:
            bool: 是否可以挽救
        """
        try:
            if X.ndim == 2:
                X = X[np.newaxis, ...]

            n_trials, n_channels, n_samples = X.shape

            # 统计每个通道的有效性
            valid_channels = 0

            for ch in range(n_channels):
                channel_data = X[:, ch, :]
                invalid_ratio = np.sum(np.isnan(channel_data) | np.isinf(channel_data)) / channel_data.size

                # 如果通道的无效值比例小于50%，认为该通道可用
                if invalid_ratio < 0.5:
                    valid_channels += 1

            # 如果至少有一半的通道可用，认为数据可以挽救
            min_required_channels = max(2, n_channels // 2)  # 至少需要2个通道或一半通道

            logger.info(f"可用通道数: {valid_channels}/{n_channels}, 最少需要: {min_required_channels}")

            return valid_channels >= min_required_channels

        except Exception as e:
            logger.error(f"数据挽救性检查失败: {e}")
            return False

    def _interpolate_channel(self, channel_data: np.ndarray, invalid_mask: np.ndarray) -> np.ndarray:
        """
        对单个通道进行插值修复

        Args:
            channel_data: 通道数据 [n_trials, n_samples]
            invalid_mask: 无效值掩码

        Returns:
            修复后的通道数据
        """
        try:
            repaired_data = np.copy(channel_data)

            for trial in range(channel_data.shape[0]):
                trial_data = repaired_data[trial, :]
                trial_mask = invalid_mask[trial, :]

                if np.any(trial_mask):
                    # 找到有效数据点
                    valid_indices = np.where(~trial_mask)[0]
                    invalid_indices = np.where(trial_mask)[0]

                    if len(valid_indices) > 1:
                        # 线性插值
                        repaired_data[trial, invalid_indices] = np.interp(
                            invalid_indices, valid_indices, trial_data[valid_indices]
                        )
                    else:
                        # 如果有效点太少，用均值填充
                        if len(valid_indices) > 0:
                            repaired_data[trial, invalid_indices] = np.mean(trial_data[valid_indices])
                        else:
                            repaired_data[trial, invalid_indices] = 0.0

            return repaired_data

        except Exception as e:
            logger.error(f"通道插值修复失败: {e}")
            return channel_data

    def _replace_with_neighbors(self, X: np.ndarray, target_ch: int, invalid_mask: np.ndarray) -> np.ndarray:
        """
        使用邻近通道的平均值替换损坏通道

        Args:
            X: 完整数据 [n_trials, n_channels, n_samples]
            target_ch: 目标通道索引
            invalid_mask: 无效值掩码 [n_trials, n_samples]

        Returns:
            修复后的通道数据 [n_trials, n_samples]
        """
        try:
            n_trials, n_channels, n_samples = X.shape
            repaired_data = np.copy(X[:, target_ch, :])

            # 找到邻近的有效通道
            neighbor_channels = []
            for ch in range(n_channels):
                if ch != target_ch:
                    # 检查该通道是否相对健康
                    ch_invalid_ratio = np.sum(np.isnan(X[:, ch, :]) | np.isinf(X[:, ch, :])) / X[:, ch, :].size
                    if ch_invalid_ratio < 0.3:  # 该通道相对健康
                        neighbor_channels.append(ch)

            if len(neighbor_channels) > 0:
                # 使用邻近通道的平均值
                neighbor_data = X[:, neighbor_channels, :]
                neighbor_mean = np.nanmean(neighbor_data, axis=1)  # [n_trials, n_samples]

                # 只替换无效的位置
                repaired_data[invalid_mask] = neighbor_mean[invalid_mask]

                logger.info(f"通道{target_ch+1}使用{len(neighbor_channels)}个邻近通道修复: {[ch+1 for ch in neighbor_channels]}")
            else:
                # 没有健康的邻近通道，使用低幅度噪声
                logger.warning(f"通道{target_ch+1}无可用邻近通道，使用低幅度噪声替代")
                noise_amplitude = 0.1
                noise_shape = np.sum(invalid_mask)
                repaired_data[invalid_mask] = np.random.normal(0, noise_amplitude, noise_shape)

            return repaired_data

        except Exception as e:
            logger.error(f"邻近通道替换失败: {e}")
            return X[:, target_ch, :]
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'BaseFeatureExtractor':
        """
        拟合特征提取器
        
        Args:
            X: 训练数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]
            
        Returns:
            self
        """
        start_time = time.time()
        
        try:
            # 验证输入
            if not self.validate_input(X, y):
                raise ValueError("输入数据验证失败")

            # 使用高级方法清理数据中的NaN和Inf值
            if np.any(np.isnan(X)) or np.any(np.isinf(X)):
                logger.info("检测到无效值，使用高级清理方法")
                X = self._clean_data_advanced(X)
            else:
                X = self._clean_data(X)

            # 转换数据类型
            X = X.astype(np.float64)  # MNE需要float64
            y = np.array(y, dtype=np.int32)
            
            logger.info(f"开始拟合{self.__class__.__name__}，数据形状: {X.shape}")
            
            # 调用子类实现
            self._fit(X, y)
            
            # 更新状态
            self._is_fitted = True
            self._feature_names = self.get_feature_names()
            
            # 记录性能
            fit_time = time.time() - start_time
            self._performance_stats['fit_time'] += fit_time
            self._performance_stats['n_fit_calls'] += 1
            
            logger.info(f"{self.__class__.__name__}拟合完成，耗时: {fit_time:.3f}秒")
            
            return self
            
        except Exception as e:
            logger.error(f"{self.__class__.__name__}拟合失败: {e}")
            raise
    
    def transform(self, X: np.ndarray) -> np.ndarray:
        """
        变换数据为特征
        
        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]
            
        Returns:
            特征矩阵 [n_trials, n_features]
        """
        start_time = time.time()
        
        try:
            # 检查是否已拟合
            if not self._is_fitted:
                raise ValueError(f"{self.__class__.__name__}尚未拟合，请先调用fit方法")
            
            # 验证输入
            if not self.validate_input(X):
                raise ValueError("输入数据验证失败")

            # 清理数据中的NaN和Inf值
            X = self._clean_data(X)

            # 转换数据类型
            X = X.astype(np.float64)  # MNE需要float64
            
            logger.debug(f"开始变换{self.__class__.__name__}，数据形状: {X.shape}")
            
            # 调用子类实现
            features = self._transform(X)
            
            # 验证输出
            if features.shape[0] != X.shape[0]:
                raise ValueError("输出特征数量与输入试次数不匹配")
            
            # 记录性能
            transform_time = time.time() - start_time
            self._performance_stats['transform_time'] += transform_time
            self._performance_stats['n_transform_calls'] += 1
            
            logger.debug(f"{self.__class__.__name__}变换完成，"
                        f"输出形状: {features.shape}，耗时: {transform_time:.3f}秒")
            
            return features
            
        except Exception as e:
            logger.error(f"{self.__class__.__name__}变换失败: {e}")
            raise
    
    def fit_transform(self, X: np.ndarray, y: np.ndarray) -> np.ndarray:
        """
        拟合并变换数据
        
        Args:
            X: 训练数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]
            
        Returns:
            特征矩阵 [n_trials, n_features]
        """
        return self.fit(X, y).transform(X)
    
    def save_model(self, filepath: str):
        """
        保存模型到文件
        
        Args:
            filepath: 保存路径
        """
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            with open(filepath, 'wb') as f:
                pickle.dump(self, f)
            logger.info(f"模型已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            raise
    
    @classmethod
    def load_model(cls, filepath: str) -> 'BaseFeatureExtractor':
        """
        从文件加载模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            加载的模型实例
        """
        try:
            with open(filepath, 'rb') as f:
                model = pickle.load(f)
            logger.info(f"模型已从{filepath}加载")
            return model
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            性能统计字典
        """
        stats = self._performance_stats.copy()
        if stats['n_fit_calls'] > 0:
            stats['avg_fit_time'] = stats['fit_time'] / stats['n_fit_calls']
        if stats['n_transform_calls'] > 0:
            stats['avg_transform_time'] = stats['transform_time'] / stats['n_transform_calls']
        return stats
    
    def reset_performance_stats(self):
        """重置性能统计"""
        self._performance_stats = {
            'fit_time': 0.0,
            'transform_time': 0.0,
            'n_fit_calls': 0,
            'n_transform_calls': 0
        }
    
    def get_params(self, deep: bool = True) -> Dict[str, Any]:
        """
        获取参数（sklearn兼容）
        
        Args:
            deep: 是否深度获取参数
            
        Returns:
            参数字典
        """
        return {
            'sampling_rate': self.sampling_rate,
            'n_channels': self.n_channels,
            'n_samples': self.n_samples,
            'enable_caching': self.enable_caching,
            'cache_dir': self.cache_dir
        }
    
    def set_params(self, **params) -> 'BaseFeatureExtractor':
        """
        设置参数（sklearn兼容）
        
        Args:
            **params: 参数字典
            
        Returns:
            self
        """
        for key, value in params.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                raise ValueError(f"无效参数: {key}")
        return self
    
    def __repr__(self) -> str:
        """字符串表示"""
        return (f"{self.__class__.__name__}("
                f"sampling_rate={self.sampling_rate}, "
                f"n_channels={self.n_channels}, "
                f"n_samples={self.n_samples}, "
                f"fitted={self._is_fitted})")
