# -*- coding: utf-8 -*-
"""
动态权重管理器
Dynamic Weight Manager

基于2024年最新研究，实现动态权重调整机制
提高多分类器集成的稳定性和性能
"""

import numpy as np
from typing import Dict, List, Optional, Tuple
from collections import deque
import logging
import time

logger = logging.getLogger(__name__)


class DynamicWeightManager:
    """
    动态权重管理器
    
    根据分类器的实时性能动态调整权重，
    提高集成系统的稳定性和适应性
    """
    
    def __init__(self, 
                 classifier_names: List[str],
                 window_size: int = 20,
                 update_frequency: int = 10,
                 temperature: float = 2.0,
                 min_weight: float = 0.05,
                 performance_decay: float = 0.95):
        """
        初始化动态权重管理器
        
        Args:
            classifier_names: 分类器名称列表
            window_size: 性能窗口大小
            update_frequency: 权重更新频率（每N次预测更新一次）
            temperature: softmax温度参数
            min_weight: 最小权重阈值
            performance_decay: 性能衰减因子
        """
        self.classifier_names = classifier_names
        self.window_size = window_size
        self.update_frequency = update_frequency
        self.temperature = temperature
        self.min_weight = min_weight
        self.performance_decay = performance_decay
        
        # 初始化权重（均匀分布）
        n_classifiers = len(classifier_names)
        self.current_weights = {name: 1.0 / n_classifiers for name in classifier_names}
        
        # 性能历史记录
        self.performance_history = {name: deque(maxlen=window_size) for name in classifier_names}
        self.prediction_count = 0
        self.last_update_time = time.time()
        
        # 统计信息
        self.weight_update_count = 0
        self.performance_stats = {name: {'correct': 0, 'total': 0} for name in classifier_names}
        
        logger.info(f"动态权重管理器初始化完成，管理{n_classifiers}个分类器")
    
    def update_performance(self, 
                          classifier_predictions: Dict[str, int],
                          classifier_probabilities: Dict[str, np.ndarray],
                          true_label: Optional[int] = None,
                          confidence_scores: Optional[Dict[str, float]] = None) -> None:
        """
        更新分类器性能记录
        
        Args:
            classifier_predictions: 各分类器的预测结果
            classifier_probabilities: 各分类器的概率输出
            true_label: 真实标签（如果可用）
            confidence_scores: 置信度分数（如果可用）
        """
        try:
            self.prediction_count += 1
            
            # 计算各分类器的性能分数
            for name in self.classifier_names:
                if name in classifier_predictions:
                    score = self._calculate_performance_score(
                        name, 
                        classifier_predictions[name],
                        classifier_probabilities.get(name),
                        true_label,
                        confidence_scores.get(name) if confidence_scores else None
                    )
                    
                    self.performance_history[name].append(score)
                    
                    # 更新统计信息
                    if true_label is not None:
                        self.performance_stats[name]['total'] += 1
                        if classifier_predictions[name] == true_label:
                            self.performance_stats[name]['correct'] += 1
            
            # 检查是否需要更新权重
            if self.prediction_count % self.update_frequency == 0:
                self._update_weights()
                
        except Exception as e:
            logger.error(f"更新性能记录失败: {e}")
    
    def _calculate_performance_score(self, 
                                   classifier_name: str,
                                   prediction: int,
                                   probabilities: Optional[np.ndarray],
                                   true_label: Optional[int],
                                   confidence: Optional[float]) -> float:
        """
        计算分类器性能分数
        
        Args:
            classifier_name: 分类器名称
            prediction: 预测结果
            probabilities: 概率输出
            true_label: 真实标签
            confidence: 置信度
            
        Returns:
            性能分数 (0-1之间)
        """
        score = 0.5  # 基础分数
        
        # 如果有真实标签，使用准确性
        if true_label is not None:
            if prediction == true_label:
                score += 0.3  # 正确预测奖励
            else:
                score -= 0.2  # 错误预测惩罚
        
        # 使用概率置信度
        if probabilities is not None:
            max_prob = np.max(probabilities)
            # 高置信度预测获得奖励
            confidence_bonus = (max_prob - 0.5) * 0.4
            score += confidence_bonus
        
        # 使用外部置信度分数
        if confidence is not None:
            score += (confidence - 0.5) * 0.2
        
        # 确保分数在合理范围内
        return np.clip(score, 0.0, 1.0)
    
    def _update_weights(self) -> None:
        """更新分类器权重"""
        try:
            # 计算各分类器的平均性能
            avg_performances = {}
            for name in self.classifier_names:
                if self.performance_history[name]:
                    # 使用指数衰减加权平均
                    performances = list(self.performance_history[name])
                    weights = [self.performance_decay ** i for i in range(len(performances))]
                    weights.reverse()  # 最新的性能权重最大
                    
                    weighted_avg = np.average(performances, weights=weights)
                    avg_performances[name] = weighted_avg
                else:
                    avg_performances[name] = 0.5  # 默认性能
            
            # 使用softmax计算新权重
            performance_values = np.array(list(avg_performances.values()))
            exp_values = np.exp(performance_values / self.temperature)
            softmax_weights = exp_values / np.sum(exp_values)
            
            # 应用最小权重约束
            softmax_weights = np.maximum(softmax_weights, self.min_weight)
            softmax_weights = softmax_weights / np.sum(softmax_weights)  # 重新归一化
            
            # 更新权重
            old_weights = self.current_weights.copy()
            for i, name in enumerate(self.classifier_names):
                self.current_weights[name] = softmax_weights[i]
            
            self.weight_update_count += 1
            self.last_update_time = time.time()
            
            # 记录权重变化
            weight_changes = {name: abs(self.current_weights[name] - old_weights[name]) 
                            for name in self.classifier_names}
            max_change = max(weight_changes.values())
            
            if max_change > 0.05:  # 只记录显著变化
                logger.info(f"权重更新 #{self.weight_update_count}: 最大变化 {max_change:.3f}")
                for name in self.classifier_names:
                    logger.debug(f"  {name}: {old_weights[name]:.3f} → {self.current_weights[name]:.3f}")
                    
        except Exception as e:
            logger.error(f"权重更新失败: {e}")
    
    def get_current_weights(self) -> Dict[str, float]:
        """获取当前权重"""
        return self.current_weights.copy()
    
    def get_performance_stats(self) -> Dict[str, Dict]:
        """获取性能统计信息"""
        stats = {}
        for name in self.classifier_names:
            total = self.performance_stats[name]['total']
            correct = self.performance_stats[name]['correct']
            accuracy = correct / total if total > 0 else 0.0
            
            recent_performance = (
                np.mean(list(self.performance_history[name])) 
                if self.performance_history[name] else 0.5
            )
            
            stats[name] = {
                'accuracy': accuracy,
                'recent_performance': recent_performance,
                'current_weight': self.current_weights[name],
                'total_predictions': total
            }
        
        return stats
    
    def reset_performance_history(self) -> None:
        """重置性能历史记录"""
        for name in self.classifier_names:
            self.performance_history[name].clear()
            self.performance_stats[name] = {'correct': 0, 'total': 0}
        
        # 重置为均匀权重
        n_classifiers = len(self.classifier_names)
        self.current_weights = {name: 1.0 / n_classifiers for name in self.classifier_names}
        
        self.prediction_count = 0
        self.weight_update_count = 0
        
        logger.info("动态权重管理器已重置")
    
    def is_stable(self, stability_threshold: float = 0.02) -> bool:
        """
        检查权重是否稳定
        
        Args:
            stability_threshold: 稳定性阈值
            
        Returns:
            权重是否稳定
        """
        if self.weight_update_count < 3:
            return False
        
        # 检查最近的权重变化
        recent_changes = []
        for name in self.classifier_names:
            if len(self.performance_history[name]) >= 2:
                recent_perf = list(self.performance_history[name])[-2:]
                change = abs(recent_perf[-1] - recent_perf[-2])
                recent_changes.append(change)
        
        if recent_changes:
            avg_change = np.mean(recent_changes)
            return avg_change < stability_threshold
        
        return False
    
    def __repr__(self) -> str:
        """字符串表示"""
        return (f"DynamicWeightManager("
                f"classifiers={len(self.classifier_names)}, "
                f"predictions={self.prediction_count}, "
                f"updates={self.weight_update_count})")
