"""
特征提取配置管理模块

提供特征提取算法的配置管理，支持通过配置文件灵活开启/关闭各种算法。
"""

import json
import os
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, asdict
import logging
from utils.path_manager import get_config_file_in_dir

logger = logging.getLogger(__name__)

@dataclass
class FBCSPConfig:
    """FBCSP特征提取配置"""
    enabled: bool = True
    freq_bands: List[Tuple[float, float]] = None
    n_components: int = 2
    reg: Optional[str] = 'oas'  # 正则化方法
    log: bool = True  # 是否使用对数变换
    
    def __post_init__(self):
        if self.freq_bands is None:
            # 默认6个频段，避免过拟合
            self.freq_bands = [
                (8, 12), (12, 16), (16, 20), (20, 24),
                (24, 30), (30, 35)
            ]

@dataclass
class RiemannianConfig:
    """Riemannian几何特征提取配置"""
    enabled: bool = True
    metric: str = 'riemann'  # Riemannian度量: 'riemann', 'logeuclid', 'euclid'
    estimator: str = 'cov'   # 协方差估计器: 'cov', 'oas', 'lwf', 'mcd', 'sch'
    tangent_space: bool = True  # 是否使用切空间映射
    reference_method: str = 'mean'  # 参考点计算方法: 'mean', 'median'
    regularization: float = 1e-6    # 协方差矩阵正则化参数

@dataclass
class TEFConfig:
    """TEF（时域-熵域-频域）特征提取配置"""
    enabled: bool = True
    
    # 时域特征配置
    time_features: Dict[str, bool] = None
    
    # 熵域特征配置  
    entropy_features: Dict[str, bool] = None
    
    # 频域特征配置
    freq_features: Dict[str, bool] = None
    
    # 频域分析参数
    freq_bands: List[Tuple[float, float]] = None
    nperseg: int = 125  # FFT窗口长度

    # 🔧 特征选择配置
    feature_selection: Optional[Dict] = None
    
    def __post_init__(self):
        if self.time_features is None:
            self.time_features = {
                'mean': True, 'std': True, 'var': True, 'skew': True,
                'kurtosis': True, 'rms': True, 'peak': True, 'energy': True
            }
            
        if self.entropy_features is None:
            self.entropy_features = {
                'sample_entropy': True, 'approximate_entropy': True,
                'permutation_entropy': False, 'fuzzy_entropy': False,
                'multiscale_entropy': False
            }
            
        if self.freq_features is None:
            self.freq_features = {
                'power_spectral_density': True, 'spectral_centroid': True,
                'spectral_bandwidth': True, 'spectral_rolloff': True,
                'spectral_flatness': True, 'spectral_crest': True,
                'dominant_frequency': True, 'frequency_variance': True
            }
            
        if self.freq_bands is None:
            self.freq_bands = [
                (0.5, 4),   # Delta
                (4, 8),     # Theta  
                (8, 13),    # Alpha
                (13, 30),   # Beta
                (30, 50)    # Gamma
            ]

@dataclass
class TangentSpaceConfig:
    """切空间特征提取配置"""
    enabled: bool = True
    estimator: str = 'oas'  # 协方差估计器
    metric: str = 'riemann'  # 黎曼度量
    regularization: float = 1e-6  # 正则化参数
    classifier: Optional[Dict[str, Any]] = None  # 分类器配置

    def __post_init__(self):
        if self.classifier is None:
            self.classifier = {
                'type': 'LogisticRegression',
                'C': 1.0,
                'max_iter': 1000,
                'random_state': 42,
                'solver': 'lbfgs'
            }

@dataclass
class PLVConfig:
    """PLV（相位锁定值）特征提取配置"""
    enabled: bool = True
    freq_bands: List[Tuple[float, float]] = None  # 频段列表
    channel_pairs: List[Tuple[int, int]] = None   # 通道对列表（1-based索引）
    filter_order: int = 4  # 滤波器阶数
    method: str = 'hilbert'  # 相位提取方法
    window_length: int = 250  # 窗口长度
    overlap: float = 0.5  # 重叠比例
    normalize: bool = True  # 是否标准化

    def __post_init__(self):
        if self.freq_bands is None:
            # 默认α波和β波频段
            self.freq_bands = [(8, 13), (13, 30)]

        if self.channel_pairs is None:
            # 默认通道对（基于8通道布局：PZ, P3, P4, C3, CZ, C4, F3, F4）
            self.channel_pairs = [
                (4, 6),  # C3-C4：左右运动皮层
                (5, 4),  # CZ-C3：中央-左运动皮层
                (5, 6),  # CZ-C4：中央-右运动皮层
                (4, 2),  # C3-P3：左运动-顶叶
                (6, 3),  # C4-P4：右运动-顶叶
                (5, 1),  # CZ-PZ：中央运动-顶叶
                (7, 4),  # F3-C3：左前额-运动
                (8, 6),  # F4-C4：右前额-运动
            ]

@dataclass
class FusionConfig:
    """特征融合配置"""
    method: str = 'concatenate'  # 融合方法: concatenate, weighted, pca
    normalize: bool = True  # 是否标准化特征
    feature_selection: bool = False  # 是否进行特征选择
    selection_method: str = 'variance'  # 特征选择方法
    n_features: Optional[int] = None  # 选择的特征数量
    
    # PCA降维配置（当method='pca'时使用）
    pca_components: Optional[int] = None
    pca_variance_ratio: float = 0.95

@dataclass
class FeatureExtractionConfig:
    """特征提取总配置"""
    # 基础配置
    sampling_rate: int = 125
    n_channels: int = 8
    n_samples: int = 250  # 2秒窗口

    # 各算法配置
    fbcsp: FBCSPConfig = None
    riemannian: RiemannianConfig = None
    tef: TEFConfig = None
    tangent_space: TangentSpaceConfig = None
    plv: PLVConfig = None
    fusion: FusionConfig = None
    
    # 性能配置
    enable_caching: bool = True
    cache_dir: str = None  # 将在运行时通过路径管理器设置
    max_cache_size: int = 1000  # MB
    
    # 日志配置
    log_level: str = 'INFO'
    log_performance: bool = True
    
    def __post_init__(self):
        if self.fbcsp is None:
            self.fbcsp = FBCSPConfig()
        if self.riemannian is None:
            self.riemannian = RiemannianConfig()
        if self.tef is None:
            self.tef = TEFConfig()
        if self.tangent_space is None:
            self.tangent_space = TangentSpaceConfig()
        if self.plv is None:
            self.plv = PLVConfig()
        if self.fusion is None:
            self.fusion = FusionConfig()
    
    @classmethod
    def from_file(cls, config_path: str) -> 'FeatureExtractionConfig':
        """从配置文件加载配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)

            # 过滤掉注释字段的辅助函数
            def filter_comments(data):
                """递归过滤掉以_comment开头的字段"""
                if isinstance(data, dict):
                    return {k: filter_comments(v) for k, v in data.items()
                           if not k.startswith('_comment')}
                elif isinstance(data, list):
                    return [filter_comments(item) for item in data]
                else:
                    return data

            # 过滤注释字段
            filtered_config_dict = filter_comments(config_dict)

            # 递归创建配置对象
            def dict_to_config(config_class, config_data):
                if config_data is None:
                    return config_class()

                # 处理嵌套配置
                for field_name, field_type in config_class.__annotations__.items():
                    if hasattr(field_type, '__annotations__'):  # 是dataclass
                        if field_name in config_data:
                            config_data[field_name] = dict_to_config(
                                field_type, config_data[field_name]
                            )

                return config_class(**config_data)

            return dict_to_config(cls, filtered_config_dict)

        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            logger.info("使用默认配置")
            return cls()
    
    def to_file(self, config_path: str):
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(self), f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存到: {config_path}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def get_enabled_extractors(self) -> List[str]:
        """获取启用的特征提取器列表"""
        enabled = []
        if self.fbcsp.enabled:
            enabled.append('fbcsp')
        if self.riemannian.enabled:
            enabled.append('riemannian')
        if self.tef.enabled:
            enabled.append('tef')
        if self.tangent_space.enabled:
            enabled.append('tangent_space')
        if self.plv.enabled:
            enabled.append('plv')
        return enabled
    
    def get_total_features(self) -> int:
        """计算总特征维度"""
        total = 0
        if self.fbcsp.enabled:
            total += len(self.fbcsp.freq_bands) * self.fbcsp.n_components
        if self.riemannian.enabled:
            total += self.n_channels * (self.n_channels + 1) // 2  # 协方差矩阵上三角
        if self.tef.enabled:
            # 计算TEF特征维度
            time_count = sum(self.tef.time_features.values()) * self.n_channels
            entropy_count = sum(self.tef.entropy_features.values()) * self.n_channels
            freq_count = sum(self.tef.freq_features.values()) * self.n_channels
            total += time_count + entropy_count + freq_count
        return total
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        try:
            # 检查至少启用一个算法
            if not any([self.fbcsp.enabled, self.riemannian.enabled, self.tef.enabled, self.tangent_space.enabled]):
                logger.error("至少需要启用一个特征提取算法")
                return False
            
            # 检查采样率和样本数
            if self.sampling_rate <= 0 or self.n_samples <= 0:
                logger.error("采样率和样本数必须大于0")
                return False
            
            # 检查通道数
            if self.n_channels <= 0:
                logger.error("通道数必须大于0")
                return False
            
            # 检查FBCSP配置
            if self.fbcsp.enabled:
                if not self.fbcsp.freq_bands:
                    logger.error("FBCSP频段配置不能为空")
                    return False
                for low, high in self.fbcsp.freq_bands:
                    if low >= high or low < 0 or high > self.sampling_rate / 2:
                        logger.error(f"无效的频段: ({low}, {high})")
                        return False
            
            logger.info("配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False

# 默认配置文件路径（使用优化配置作为默认）
DEFAULT_CONFIG_FILENAME = 'feature_extraction_optimized.json'

def load_config(config_path: str = None) -> FeatureExtractionConfig:
    """加载特征提取配置"""
    if config_path is None:
        config_path = str(get_config_file_in_dir(DEFAULT_CONFIG_FILENAME))

    if os.path.exists(config_path):
        return FeatureExtractionConfig.from_file(config_path)
    else:
        logger.info(f"配置文件不存在: {config_path}，使用默认配置")
        config = FeatureExtractionConfig()
        # 在打包环境中不要创建配置文件，避免在exe同级目录创建文件
        import sys
        if not hasattr(sys, '_MEIPASS'):
            # 只在开发环境中创建默认配置文件
            try:
                # 确保配置目录存在
                from utils.path_manager import path_manager
                config_dir = os.path.dirname(config_path)
                path_manager.ensure_directory_exists(config_dir)
                config.to_file(config_path)
                logger.info(f"已创建默认配置文件: {config_path}")
            except Exception as e:
                logger.warning(f"创建默认配置文件失败: {e}")
        return config

def save_config(config: FeatureExtractionConfig, config_path: str = None):
    """保存特征提取配置"""
    if config_path is None:
        config_path = DEFAULT_CONFIG_PATH
    config.to_file(config_path)
