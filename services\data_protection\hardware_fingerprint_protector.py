"""
硬件指纹数据保护器

基于系统现有的硬件指纹机制，为康复评估数据提供加密保护。
使用设备的硬件指纹生成唯一密钥，确保数据只能在原设备或拥有硬件信息的设备上解密。
"""

import os
import hashlib
import base64
import time
import json
import pickle
import numpy as np
from typing import Dict, Any, Optional, Tuple
from cryptography.fernet import Fernet

from services.license_manager import StableHardwareID


class HardwareFingerprintDataProtector:
    """基于硬件指纹的数据保护器"""
    
    def __init__(self, hardware_id: Optional[str] = None):
        """
        初始化数据保护器
        
        Args:
            hardware_id: 指定的硬件ID，如果为None则使用当前设备的硬件ID
        """
        if hardware_id:
            self.hardware_id = hardware_id
        else:
            # 使用当前设备的硬件指纹
            self.hardware_id = StableHardwareID.generate_hardware_id()
        
        # 基于硬件ID生成加密密钥
        self.encryption_key = self._generate_encryption_key()
        self.cipher = Fernet(self.encryption_key)
    
    def _generate_encryption_key(self) -> bytes:
        """
        基于硬件ID生成加密密钥

        Returns:
            bytes: Fernet兼容的加密密钥
        """
        # 使用硬件ID + 固定盐值生成密钥材料
        key_material = f"{self.hardware_id}_NK_BCI_REHABILITATION_2024"
        key_hash = hashlib.sha256(key_material.encode()).digest()
        return base64.urlsafe_b64encode(key_hash[:32])

    def _generate_hardware_id_hash(self, hardware_id: str) -> str:
        """
        生成硬件ID的安全哈希值

        Args:
            hardware_id: 原始硬件ID

        Returns:
            str: 安全的哈希值
        """
        # 使用多重哈希和盐值保护硬件ID
        salt = "NK_BCI_HWID_PROTECTION_2024"
        combined = f"{hardware_id}_{salt}_{time.time()//86400}"  # 按天变化的盐值

        # 多重哈希
        hash1 = hashlib.sha256(combined.encode()).hexdigest()
        hash2 = hashlib.sha512(f"{hash1}_{salt}".encode()).hexdigest()

        # 返回截断的哈希值
        return hash2[:32]
    
    def save_protected_data(self, 
                          data_dict: Dict[str, Any], 
                          filepath: str, 
                          patient_id: Optional[str] = None,
                          session_info: Optional[Dict[str, Any]] = None) -> Tuple[str, str]:
        """
        保存加密数据并生成硬件信息文件
        
        Args:
            data_dict: 要保存的数据字典
            filepath: 文件路径（.npz格式，会自动转换为.nkd）
            patient_id: 患者ID
            session_info: 会话信息
            
        Returns:
            Tuple[str, str]: (加密数据文件路径, 硬件信息文件路径)
        """
        try:
            # 准备数据包
            protected_data = {
                'hardware_id': self.hardware_id,
                'timestamp': time.time(),
                'patient_id': patient_id,
                'session_info': session_info or {},
                'data': data_dict
            }
            
            # 序列化并加密
            serialized = pickle.dumps(protected_data)
            encrypted_data = self.cipher.encrypt(serialized)
            
            # 生成加密文件路径
            protected_file = filepath.replace('.npz', '.nkd')

            # 确保目录存在
            dir_path = os.path.dirname(protected_file)
            if dir_path:  # 只有当目录路径不为空时才创建
                os.makedirs(dir_path, exist_ok=True)
            
            # 保存加密文件
            with open(protected_file, 'wb') as f:
                f.write(encrypted_data)
            
            # 生成硬件信息文件（使用哈希保护硬件ID）
            hardware_info_file = filepath.replace('.npz', '.hwinfo')

            # 对硬件ID进行多重哈希保护
            device_signature = self._generate_hardware_id_hash(self.hardware_id)

            # 清理session_info中的敏感信息
            clean_session_info = {}
            if session_info:
                for key, value in session_info.items():
                    # 排除包含硬件信息的字段
                    if 'hardware' not in key.lower() and 'device' not in key.lower():
                        clean_session_info[key] = value

            hardware_info = {
                'device_signature': device_signature,  # 使用模糊字段名和哈希值
                'subject_id': patient_id,  # 使用模糊字段名
                'creation_time': time.time(),  # 使用模糊字段名
                'session_metadata': clean_session_info,  # 清理后的会话信息
                'target_file': protected_file,  # 使用模糊字段名
                'content_types': list(data_dict.keys()) if isinstance(data_dict, dict) else [],
                'payload_size': len(encrypted_data),  # 使用模糊字段名
                'protection_version': 'v2.0_secure'  # 版本标识
            }
            
            with open(hardware_info_file, 'w', encoding='utf-8') as f:
                json.dump(hardware_info, f, indent=2, ensure_ascii=False)
            
            return protected_file, hardware_info_file
            
        except Exception as e:
            raise RuntimeError(f"保存加密数据失败: {e}")
    
    def get_hardware_id(self) -> str:
        """获取当前硬件ID"""
        return self.hardware_id
    
    @staticmethod
    def get_current_hardware_id() -> str:
        """获取当前设备的硬件ID"""
        return StableHardwareID.generate_hardware_id()

    def verify_hardware_compatibility(self, hwinfo_file_path: str) -> bool:
        """
        验证当前设备是否与加密数据兼容（不暴露硬件ID明文）

        Args:
            hwinfo_file_path: 硬件信息文件路径

        Returns:
            bool: 是否兼容
        """
        try:
            import json

            # 读取硬件信息文件
            with open(hwinfo_file_path, 'r', encoding='utf-8') as f:
                hwinfo = json.load(f)

            # 支持新旧字段名
            stored_signature = hwinfo.get('device_signature') or hwinfo.get('hardware_id_hash')
            if not stored_signature:
                return False

            # 生成当前设备的签名进行比较
            current_signature = self._generate_hardware_id_hash(self.hardware_id)

            return stored_signature == current_signature

        except Exception:
            return False

    @staticmethod
    def create_secure_hardware_info_summary(hwinfo_file_path: str) -> Dict[str, Any]:
        """
        创建安全的硬件信息摘要（用于显示，不包含敏感信息）

        Args:
            hwinfo_file_path: 硬件信息文件路径

        Returns:
            Dict: 安全的硬件信息摘要
        """
        try:
            import json

            with open(hwinfo_file_path, 'r', encoding='utf-8') as f:
                hwinfo = json.load(f)

            # 支持新旧字段名，并生成安全摘要
            device_signature = hwinfo.get('device_signature') or hwinfo.get('hardware_id_hash', 'Unknown')

            # 从设备签名生成安全的部分显示（前4位+后4位）
            if device_signature and device_signature != 'Unknown':
                device_display = device_signature[:4] + '********' + device_signature[-4:]
            else:
                device_display = 'Unknown'

            return {
                'device_display': device_display,  # 设备签名的安全显示
                'subject_id': hwinfo.get('subject_id') or hwinfo.get('patient_id', 'Unknown'),
                'creation_time': hwinfo.get('creation_time') or hwinfo.get('timestamp', 0),
                'payload_size': hwinfo.get('payload_size') or hwinfo.get('file_size', 0),
                'protection_version': hwinfo.get('protection_version') or hwinfo.get('encryption_method', 'Unknown'),
                'content_types': hwinfo.get('content_types') or hwinfo.get('data_keys', [])
            }

        except Exception:
            return {'error': 'Failed to read metadata'}


class RehabilitationDataCollector:
    """康复评估数据收集器"""
    
    def __init__(self, hospital_id: Optional[str] = None):
        """
        初始化数据收集器
        
        Args:
            hospital_id: 医院标识
        """
        self.hospital_id = hospital_id or "unknown_hospital"
        self.protector = HardwareFingerprintDataProtector()
        self.device_hardware_id = self.protector.get_hardware_id()

        # 使用路径管理器创建数据存储根目录
        from utils.path_manager import get_data_file
        self.data_root = str(get_data_file("rehabilitation_raw"))
        os.makedirs(self.data_root, exist_ok=True)
        
        # print(f"🔧 康复数据收集器初始化完成")
        # print(f"   医院ID: {self.hospital_id}")
        # print(f"   设备硬件ID: {self.device_hardware_id}")
    
    def save_training_data(self, 
                          patient_id: str,
                          session_date: str,
                          session_time: str,
                          motor_imagery_data: np.ndarray,
                          rest_data: np.ndarray,
                          motor_timestamps: np.ndarray,
                          rest_timestamps: np.ndarray,
                          motor_trial_numbers: np.ndarray,
                          rest_trial_numbers: np.ndarray) -> str:
        """
        保存训练数据
        
        Args:
            patient_id: 患者ID
            session_date: 会话日期
            session_time: 会话时间
            motor_imagery_data: 运动想象数据 [n_trials, 8, 250]
            rest_data: 平静状态数据 [n_trials, 8, 250]
            motor_timestamps: 运动想象时间戳
            rest_timestamps: 平静状态时间戳
            motor_trial_numbers: 运动想象试次编号
            rest_trial_numbers: 平静状态试次编号
            
        Returns:
            str: 会话目录路径
        """
        try:
            # 创建会话目录（处理时间中的冒号，避免Windows路径问题）
            safe_session_time = session_time.replace(':', '-')
            session_dir = os.path.join(
                self.data_root,
                patient_id,
                f"{session_date}_session_{safe_session_time}"
            )
            os.makedirs(session_dir, exist_ok=True)
            
            # 会话信息（不包含敏感硬件信息）
            session_info = {
                'subject_id': patient_id,  # 使用模糊字段名
                'facility_id': self.hospital_id,  # 使用模糊字段名
                'session_date': session_date,
                'session_time': session_time,
                'motor_trials': len(motor_imagery_data),
                'rest_trials': len(rest_data),
                'sampling_rate': 125,
                'channels': ["PZ", "P3", "P4", "C3", "CZ", "C4", "F3", "F4"],
                'data_shape': [8, 250]  # 8通道，250采样点（2秒@125Hz）
            }
            
            # 保存运动想象数据
            motor_data = {
                'data': motor_imagery_data,
                'timestamps': motor_timestamps,
                'trial_numbers': motor_trial_numbers,
                'label': 'motor_imagery'
            }
            
            motor_file = os.path.join(session_dir, 'motor_imagery_raw.npz')
            motor_protected, motor_hwinfo = self.protector.save_protected_data(
                motor_data, motor_file, patient_id, session_info
            )
            
            # 保存平静状态数据
            rest_data_dict = {
                'data': rest_data,
                'timestamps': rest_timestamps,
                'trial_numbers': rest_trial_numbers,
                'label': 'rest'
            }
            
            rest_file = os.path.join(session_dir, 'rest_raw.npz')
            rest_protected, rest_hwinfo = self.protector.save_protected_data(
                rest_data_dict, rest_file, patient_id, session_info
            )
            
            # 保存会话信息
            info_file = os.path.join(session_dir, 'session_info.npz')
            info_protected, info_hwinfo = self.protector.save_protected_data(
                session_info, info_file, patient_id, session_info
            )
            
            print(f"✅ 患者 {patient_id} 康复评估数据已保存并加密")
            print(f"   会话目录: {session_dir}")
            print(f"   运动想象试次: {len(motor_imagery_data)}")
            print(f"   平静状态试次: {len(rest_data)}")
            
            return session_dir
            
        except Exception as e:
            print(f"❌ 保存康复评估数据失败: {e}")
            raise
