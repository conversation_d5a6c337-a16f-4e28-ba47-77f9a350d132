#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PLV (Phase Locking Value) 特征提取器

实现基于相位锁定值的脑电特征提取，专门针对运动想象分类任务
集成到现有特征提取框架中，支持训练和实时预测

作者: AI Assistant
创建时间: 2025-01-11
"""

import numpy as np
import logging
from typing import Optional, Dict, Any, List, Tuple
import time

from .base_extractor import BaseFeatureExtractor
from .plv_utils import PLVCalculator

logger = logging.getLogger(__name__)


class PLVExtractor(BaseFeatureExtractor):
    """
    PLV特征提取器
    
    提取基于相位锁定值的脑电特征，用于运动想象分类
    """
    
    def __init__(self,
                 freq_bands: Optional[List[Tuple[float, float]]] = None,
                 channel_pairs: Optional[List[Tuple[int, int]]] = None,
                 filter_order: int = 4,
                 **kwargs):
        """
        初始化PLV特征提取器
        
        Args:
            freq_bands: 频段列表，默认为α波和β波
            channel_pairs: 通道对列表，1-based索引
            filter_order: 滤波器阶数
        """
        super().__init__(**kwargs)
        
        # 默认频段配置
        if freq_bands is None:
            self.freq_bands = [(8, 13), (13, 30)]  # α波和β波
        else:
            self.freq_bands = freq_bands
            
        # 默认通道对配置（基于8通道布局：PZ, P3, P4, C3, CZ, C4, F3, F4）
        if channel_pairs is None:
            # 使用1-based索引，将在PLVCalculator中转换为0-based
            self.channel_pairs = [
                (4, 6),  # C3-C4：左右运动皮层
                (5, 4),  # CZ-C3：中央-左运动皮层
                (5, 6),  # CZ-C4：中央-右运动皮层
                (4, 2),  # C3-P3：左运动-顶叶
                (6, 3),  # C4-P4：右运动-顶叶
                (5, 1),  # CZ-PZ：中央运动-顶叶
                (7, 4),  # F3-C3：左前额-运动
                (8, 6),  # F4-C4：右前额-运动
            ]
        else:
            self.channel_pairs = channel_pairs
            
        self.filter_order = filter_order
        
        # PLV计算器
        self.plv_calculator = None
        
        # 特征信息
        self._n_features = None
        self._feature_names = None
        
        logger.info(f"PLV特征提取器初始化完成")
        logger.info(f"频段: {self.freq_bands}")
        logger.info(f"通道对数量: {len(self.channel_pairs)}")
    
    def _fit(self, X: np.ndarray, y: np.ndarray) -> 'PLVExtractor':
        """
        训练PLV特征提取器
        
        Args:
            X: 训练数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]
            
        Returns:
            self
        """
        try:
            logger.info(f"开始训练PLV特征提取器，数据形状: {X.shape}")
            
            if X.shape[1] != 8:
                raise ValueError(f"期望8个通道，实际得到{X.shape[1]}个通道")
            
            # 创建PLV计算器
            self.plv_calculator = PLVCalculator(
                sampling_rate=self.sampling_rate,
                freq_bands=self.freq_bands,
                channel_pairs=self.channel_pairs,
                filter_order=self.filter_order
            )
            
            # 验证配置
            if not self.plv_calculator.validate_configuration():
                raise ValueError("PLV计算器配置验证失败")
            
            # 计算特征维度
            self._n_features = len(self.freq_bands) * len(self.channel_pairs)
            self._feature_names = self.plv_calculator.get_feature_names()
            
            logger.info(f"PLV特征提取器训练完成，特征维度: {self._n_features}")
            
            return self
            
        except Exception as e:
            logger.error(f"PLV特征提取器训练失败: {e}")
            raise
    
    def _transform(self, X: np.ndarray) -> np.ndarray:
        """
        提取PLV特征
        
        Args:
            X: 输入数据 [n_trials, n_channels, n_samples]
            
        Returns:
            PLV特征 [n_trials, n_features]
        """
        try:
            if self.plv_calculator is None:
                raise ValueError("PLV特征提取器未训练，请先调用fit方法")
            
            logger.debug(f"开始PLV特征提取，数据形状: {X.shape}")
            
            n_trials = X.shape[0]
            all_features = []
            
            start_time = time.perf_counter()
            
            # 对每个试验提取PLV特征
            for trial in range(n_trials):
                trial_data = X[trial]  # [n_channels, n_samples]
                
                # 计算PLV特征
                plv_features = self.plv_calculator.calculate_plv_features(trial_data)
                all_features.append(plv_features)
                
                if trial % 10 == 0:
                    logger.debug(f"已处理试验 {trial+1}/{n_trials}")
            
            # 转换为numpy数组
            features = np.array(all_features)
            
            # 性能统计
            processing_time = (time.perf_counter() - start_time) * 1000
            avg_time_per_trial = processing_time / n_trials
            
            logger.debug(f"PLV特征提取完成，总耗时: {processing_time:.2f}ms")
            logger.debug(f"平均每试验: {avg_time_per_trial:.2f}ms")
            logger.debug(f"特征形状: {features.shape}")
            
            # 特征质量检查
            self._validate_features(features)
            
            return features
            
        except Exception as e:
            logger.error(f"PLV特征提取失败: {e}")
            raise
    
    def _validate_features(self, features: np.ndarray) -> None:
        """
        验证提取的特征质量
        
        Args:
            features: 提取的特征 [n_trials, n_features]
        """
        try:
            # 检查NaN和无穷值
            if np.any(np.isnan(features)):
                logger.warning("PLV特征中包含NaN值")
            
            if np.any(np.isinf(features)):
                logger.warning("PLV特征中包含无穷值")
            
            # 检查特征范围（PLV应该在0-1之间）
            min_val = np.min(features)
            max_val = np.max(features)
            
            if min_val < 0 or max_val > 1:
                logger.warning(f"PLV特征超出预期范围[0,1]: [{min_val:.3f}, {max_val:.3f}]")
            
            # 检查特征变异性
            feature_std = np.std(features, axis=0)
            low_variance_features = np.sum(feature_std < 1e-4)  # 放宽阈值

            if low_variance_features > 0:
                logger.debug(f"发现{low_variance_features}个低变异性PLV特征（标准差<1e-4）")
                # 显示具体的低变异性特征信息
                low_var_indices = np.where(feature_std < 1e-4)[0]
                feature_names = self.get_feature_names()
                for idx in low_var_indices[:3]:  # 只显示前3个
                    if idx < len(feature_names):
                        logger.debug(f"  {feature_names[idx]}: std={feature_std[idx]:.6f}")
            
            # 统计信息
            logger.debug(f"PLV特征统计: 均值={np.mean(features):.3f}, "
                        f"标准差={np.std(features):.3f}, "
                        f"范围=[{min_val:.3f}, {max_val:.3f}]")
            
        except Exception as e:
            logger.error(f"PLV特征验证失败: {e}")
    
    def get_feature_names(self) -> List[str]:
        """
        获取特征名称列表
        
        Returns:
            特征名称列表
        """
        if self._feature_names is None:
            if self.plv_calculator is not None:
                self._feature_names = self.plv_calculator.get_feature_names()
            else:
                # 生成默认特征名称
                feature_names = []
                channel_names = ['PZ', 'P3', 'P4', 'C3', 'CZ', 'C4', 'F3', 'F4']
                
                for freq_idx, (low_freq, high_freq) in enumerate(self.freq_bands):
                    freq_name = f"{low_freq}-{high_freq}Hz"
                    
                    for ch1, ch2 in self.channel_pairs:
                        pair_name = f"{channel_names[ch1-1]}-{channel_names[ch2-1]}"
                        feature_name = f"PLV_{freq_name}_{pair_name}"
                        feature_names.append(feature_name)
                
                self._feature_names = feature_names
        
        return self._feature_names
    
    def get_feature_info(self) -> Dict[str, Any]:
        """
        获取特征提取器信息
        
        Returns:
            特征信息字典
        """
        return {
            'extractor_type': 'PLV',
            'n_features': self._n_features,
            'freq_bands': self.freq_bands,
            'channel_pairs': self.channel_pairs,
            'filter_order': self.filter_order,
            'feature_names': self.get_feature_names()
        }
    
    def extract_single_trial(self, trial_data: np.ndarray) -> np.ndarray:
        """
        提取单个试验的PLV特征（用于实时预测）
        
        Args:
            trial_data: 单个试验数据 [n_channels, n_samples]
            
        Returns:
            PLV特征向量 [n_features]
        """
        try:
            if self.plv_calculator is None:
                raise ValueError("PLV特征提取器未训练")
            
            if trial_data.shape[0] != 8:
                raise ValueError(f"期望8个通道，实际得到{trial_data.shape[0]}个通道")
            
            # 计算PLV特征
            plv_features = self.plv_calculator.calculate_plv_features(trial_data)
            
            return plv_features
            
        except Exception as e:
            logger.error(f"单试验PLV特征提取失败: {e}")
            # 返回零特征向量作为备选
            return np.zeros(self._n_features or 16)  # 默认16个特征


def create_plv_extractor_from_config(config: Dict[str, Any]) -> PLVExtractor:
    """
    从配置创建PLV特征提取器
    
    Args:
        config: PLV配置字典
        
    Returns:
        PLV特征提取器实例
    """
    return PLVExtractor(
        freq_bands=config.get('freq_bands', [(8, 13), (13, 30)]),
        channel_pairs=config.get('channel_pairs', None),
        filter_order=config.get('filter_order', 4),
        sampling_rate=config.get('sampling_rate', 125),
        enable_caching=config.get('enable_caching', True)
    )


def test_plv_extractor():
    """
    测试PLV特征提取器
    """
    print("测试PLV特征提取器...")
    
    # 创建测试数据
    n_trials = 20
    n_channels = 8
    n_samples = 250  # 2秒数据
    
    X = np.random.randn(n_trials, n_channels, n_samples)
    y = np.random.randint(0, 2, n_trials)
    
    # 创建PLV特征提取器
    plv_extractor = PLVExtractor()
    
    try:
        # 训练
        plv_extractor.fit(X, y)
        print("✅ PLV特征提取器训练成功")
        
        # 特征提取
        features = plv_extractor.transform(X)
        print(f"✅ PLV特征提取成功，特征形状: {features.shape}")
        
        # 获取特征信息
        feature_info = plv_extractor.get_feature_info()
        print(f"特征维度: {feature_info['n_features']}")
        print(f"频段: {feature_info['freq_bands']}")
        
        # 单试验测试
        single_features = plv_extractor.extract_single_trial(X[0])
        print(f"✅ 单试验特征提取成功，特征维度: {len(single_features)}")
        
        return True
        
    except Exception as e:
        print(f"❌ PLV特征提取器测试失败: {e}")
        return False


if __name__ == "__main__":
    test_plv_extractor()
