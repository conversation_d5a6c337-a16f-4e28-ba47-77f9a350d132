#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时集成管理器

管理实时预处理管道，实现完整的实时数据流处理

作者: AI Assistant
创建时间: 2025-01-04
"""

import numpy as np
import time
import threading
import logging
from typing import Optional, Dict, Any, Callable
from collections import deque

from .preprocessing_pipeline import EEGPreprocessingPipeline
from .preprocessing_config import PreprocessingConfig


class RealtimeIntegrationManager:
    """
    实时集成管理器

    功能：
    1. 管理实时预处理管道（传统滤波 + RLS + Kalman）
    2. 提供2秒窗口数据收集
    3. 连接预处理输出到特征提取
    4. 提供统一的接口给上层应用
    """
    
    def __init__(self, config: Optional[PreprocessingConfig] = None):
        """
        初始化实时集成管理器
        
        Args:
            config: 预处理配置，如果为None则使用默认配置
        """
        if config is None:
            config = PreprocessingConfig()
        
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化预处理管道
        self.preprocessing_pipeline = EEGPreprocessingPipeline(config)

        # 状态管理
        self.is_running = False
        self.total_packets_processed = 0

        # 2秒窗口数据收集（优化：减少缓冲区大小）
        self.window_duration = 2.0  # 2秒窗口
        self.window_samples = int(self.window_duration * config.sampling_rate)  # 250样本
        self.data_buffer = deque(maxlen=int(self.window_samples * 1.2))  # 3秒缓冲（从4秒优化为3秒）
        self.display_buffer = deque(maxlen=int(self.window_samples * 1.2))  # 3秒缓冲（保持两个独立缓冲区）
        self.buffer_lock = threading.RLock()

        # 回调函数
        self.window_ready_callback = None  # 2秒窗口就绪回调
        self.packet_processed_callback = None  # 数据包处理完成回调

        # 性能统计
        self.integration_stats = {
            'total_packets': 0,
            'total_windows': 0,
            'pipeline_errors': 0
        }

        self.logger.info("实时集成管理器初始化完成")
    

    
    def _extract_window(self):
        """提取2秒窗口数据（精确250样本）- 优化版本减少内存分配"""
        try:
            with self.buffer_lock:
                if len(self.data_buffer) >= self.window_samples:
                    # 优化：直接从deque创建numpy数组，避免中间列表转换
                    # 这样减少了一次内存分配和复制操作
                    buffer_array = np.array(self.data_buffer)  # 一次性转换整个缓冲区
                    window_array = buffer_array[-self.window_samples:].T  # [8, 250] 使用切片

                    self.integration_stats['total_windows'] += 1

                    # 调用窗口就绪回调
                    if self.window_ready_callback:
                        self.window_ready_callback(window_array)

                    self.logger.debug(f"提取窗口数据完成，形状: {window_array.shape}")

        except Exception as e:
            self.logger.error(f"提取窗口数据失败: {e}")

    def start(self):
        """启动实时集成管理器"""
        try:
            self.is_running = True
            self.logger.info("实时集成管理器已启动")

        except Exception as e:
            self.logger.error(f"启动实时集成管理器失败: {e}")
            raise
    
    def stop(self):
        """停止实时集成管理器"""
        try:
            self.is_running = False

            # 清空数据缓冲区
            with self.buffer_lock:
                self.data_buffer.clear()
                self.display_buffer.clear()

            # 停止预处理管道
            if hasattr(self.preprocessing_pipeline, 'stop'):
                self.preprocessing_pipeline.stop()

            self.logger.info("实时集成管理器已停止")

        except Exception as e:
            self.logger.error(f"停止实时集成管理器失败: {e}")
    
    def process_packet(self, packet_data: np.ndarray) -> Dict[str, Any]:
        """
        处理数据包（主线程调用）
        
        Args:
            packet_data: 输入数据包 [8, 4]
            
        Returns:
            dict: 处理结果
        """
        if not self.is_running:
            return {'status': 'not_running', 'error': '集成管理器未启动'}
        
        try:
            # 通过预处理管道处理数据包
            result = self.preprocessing_pipeline.process_packet(packet_data)

            # 将处理后的数据添加到缓冲区
            if 'final_output' in result:
                processed_data = result['final_output']  # 归一化后的数据（用于分类）
                
                # 获取滤波但未归一化的数据（用于显示）
                display_data = None
                if 'traditional_output' in result and result['traditional_output']:
                    traditional_result = result['traditional_output']
                    # 优先使用归一化前的数据
                    if 'filtered_data_before_standardization' in traditional_result:
                        display_data = traditional_result['filtered_data_before_standardization']
                    elif 'channel_standardization' not in traditional_result.get('steps_applied', []):
                        # 如果传统滤波没有归一化，直接使用
                        display_data = traditional_result['filtered_data']
                    else:
                        # 回退到归一化数据
                        display_data = processed_data
                else:
                    # 没有传统滤波输出，回退到最终输出
                    display_data = processed_data

                # 添加数据到两个缓冲区
                with self.buffer_lock:
                    for sample_idx in range(processed_data.shape[1]):
                        # 分类用缓冲区（归一化数据）
                        sample = processed_data[:, sample_idx]  # [8,] 单个样本
                        self.data_buffer.append(sample)
                        
                        # 显示用缓冲区（滤波但未归一化数据）
                        display_sample = display_data[:, sample_idx]  # [8,] 单个样本
                        self.display_buffer.append(display_sample)

                    # 检查是否可以提取窗口
                    if len(self.data_buffer) >= self.window_samples:
                        self._extract_window()

                # 调用数据包处理回调（如果设置）
                if self.packet_processed_callback:
                    self.packet_processed_callback(processed_data)

            self.total_packets_processed += 1
            self.integration_stats['total_packets'] += 1

            # 添加集成管理器的状态信息
            result['integration_info'] = {
                'total_packets_processed': self.total_packets_processed,
                'buffer_size': len(self.data_buffer),
                'is_running': self.is_running
            }

            return result
            
        except Exception as e:
            self.logger.error(f"处理数据包失败: {e}")
            self.integration_stats['pipeline_errors'] += 1
            return {
                'status': 'error',
                'error': str(e),
                'integration_info': {
                    'total_packets_processed': self.total_packets_processed,
                    'is_running': self.is_running
                }
            }
    
    def set_window_ready_callback(self, callback: Callable[[np.ndarray], None]):
        """
        设置窗口就绪回调函数

        Args:
            callback: 回调函数，接收[8, 250]窗口数据
        """
        self.window_ready_callback = callback
        self.logger.info("窗口就绪回调已设置")
    
    def set_packet_processed_callback(self, callback: Callable[[np.ndarray], None]):
        """
        设置数据包处理回调函数
        
        Args:
            callback: 回调函数，接收[8, 4]数据包
        """
        self.packet_processed_callback = callback
        self.logger.info("数据包处理回调已设置")
    
    def get_current_window(self) -> Optional[np.ndarray]:
        """
        获取当前2秒窗口数据（同步方式）

        注意：为保持与预训练的一致性，返回2秒窗口（250样本）而不是3秒窗口

        Returns:
            当前窗口数据 [8, 250] 或 None
        """
        with self.buffer_lock:
            if len(self.data_buffer) >= self.window_samples:
                # 优化：减少内存分配
                buffer_array = np.array(self.data_buffer)
                return buffer_array[-self.window_samples:].T  # [8, 250]
            return None

    def get_latest_500ms_window(self) -> Optional[np.ndarray]:
        """
        从当前2秒窗口中提取最新0.5秒数据（归一化数据，用于分类）

        用于脑电地形图显示，提供高时间分辨率的实时数据

        Returns:
            最新0.5秒窗口数据 [8, 63] 或 None
        """
        with self.buffer_lock:
            if len(self.data_buffer) >= self.window_samples:
                # 计算0.5秒对应的样本数
                samples_500ms = int(0.5 * self.config.sampling_rate)  # 0.5秒 * 125Hz = 62.5 ≈ 63样本

                # 确保不超过可用数据量
                available_samples = len(self.data_buffer)
                if available_samples >= samples_500ms:
                    # 优化：减少内存分配
                    buffer_array = np.array(self.data_buffer)
                    return buffer_array[-samples_500ms:].T  # [8, 63]
            return None

    def get_latest_500ms_display_window(self) -> Optional[np.ndarray]:
        """
        从显示缓冲区中提取最新0.5秒数据（滤波但未归一化数据，用于实时曲线显示）

        Returns:
            最新0.5秒显示窗口数据 [8, 63] 或 None
        """
        with self.buffer_lock:
            if len(self.display_buffer) >= self.window_samples:
                # 计算0.5秒对应的样本数
                samples_500ms = int(0.5 * self.config.sampling_rate)  # 0.5秒 * 125Hz = 62.5 ≈ 63样本

                # 确保不超过可用数据量
                available_samples = len(self.display_buffer)
                if available_samples >= samples_500ms:
                    # 优化：减少内存分配
                    buffer_array = np.array(self.display_buffer)
                    return buffer_array[-samples_500ms:].T  # [8, 63]
            return None

    def get_status(self) -> Dict[str, Any]:
        """获取集成管理器状态"""
        return {
            'is_running': self.is_running,
            'total_packets_processed': self.total_packets_processed,
            'preprocessing_pipeline': {
                'enabled_steps': self.config.get_enabled_steps(),
                'processor_stats': self.preprocessing_pipeline.get_processor_stats()
            },
            'buffer_status': {
                'buffer_size': len(self.data_buffer),
                'window_samples': self.window_samples,
                'can_extract_window': len(self.data_buffer) >= self.window_samples
            },
            'integration_stats': self.integration_stats.copy()
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {
            'integration': self.integration_stats.copy(),
            'preprocessing': self.preprocessing_pipeline.get_performance_stats()
        }

        return stats

    def reset_stats(self):
        """重置统计信息"""
        self.integration_stats = {
            'total_packets': 0,
            'total_windows': 0,
            'pipeline_errors': 0
        }
        self.total_packets_processed = 0

        # 清空缓冲区
        with self.buffer_lock:
            self.data_buffer.clear()
            self.display_buffer.clear()

        self.logger.info("统计信息已重置")
