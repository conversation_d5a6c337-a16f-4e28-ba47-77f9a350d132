"""
完整的10名受试者验证脚本

对比预处理前后的5类算法性能差异
使用完整的前10名受试者数据进行可靠的统计分析
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import json
from datetime import datetime
from typing import List, Dict, Any
from scipy import stats

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from services.validation.test_5_algorithms import FiveAlgorithmsValidator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('validation_comprehensive_10_subjects.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class Comprehensive10SubjectsValidator:
    """完整的10名受试者验证器"""
    
    def __init__(self, dataset_path: str):
        """
        初始化完整验证器
        
        Args:
            dataset_path: EEG数据集路径
        """
        self.dataset_path = dataset_path
        
        # 完整的前10名受试者
        self.test_subjects = [f"S{i:03d}" for i in range(1, 11)]
        
        logger.info(f"完整10名受试者验证器初始化完成")
        logger.info(f"数据集路径: {dataset_path}")
        logger.info(f"测试受试者: {self.test_subjects}")
        logger.info(f"重点关注: 预处理前后效果对比")
    
    def run_comprehensive_validation(self, cv_folds: int = 5) -> Dict[str, Any]:
        """
        运行完整的10名受试者验证
        
        Args:
            cv_folds: 交叉验证折数
            
        Returns:
            完整验证结果
        """
        logger.info("开始完整的10名受试者验证")
        logger.info(f"测试受试者数量: {len(self.test_subjects)}")
        logger.info(f"交叉验证折数: {cv_folds}")
        logger.info("对比内容: 预处理前 vs 预处理后")
        
        # 验证数据集可用性
        available_subjects = self._check_dataset_availability()
        if len(available_subjects) < 5:
            raise ValueError(f"可用受试者数量不足: {len(available_subjects)}/10")
        
        logger.info(f"实际可用受试者: {len(available_subjects)}名")
        
        # 测试1: 无预处理版本
        logger.info("=" * 70)
        logger.info("阶段1: 测试无预处理版本...")
        logger.info("=" * 70)
        
        validator_no_preprocessing = FiveAlgorithmsValidator(
            self.dataset_path,
            results_dir="validation_results_10_subjects_no_preprocessing",
            enable_preprocessing=False
        )
        
        results_no_preprocessing = validator_no_preprocessing.validate_all_subjects(cv_folds)
        
        # 测试2: 有预处理版本（8-30Hz优化）
        logger.info("=" * 70)
        logger.info("阶段2: 测试有预处理版本（8-30Hz优化）...")
        logger.info("=" * 70)
        
        validator_with_preprocessing = FiveAlgorithmsValidator(
            self.dataset_path,
            results_dir="validation_results_10_subjects_with_preprocessing",
            enable_preprocessing=True
        )
        
        results_with_preprocessing = validator_with_preprocessing.validate_all_subjects(cv_folds)
        
        # 生成综合对比报告
        comprehensive_report = self._generate_comprehensive_report(
            results_no_preprocessing,
            results_with_preprocessing,
            available_subjects
        )
        
        # 保存结果
        self._save_comprehensive_results(comprehensive_report)
        
        logger.info("完整的10名受试者验证完成")
        return comprehensive_report
    
    def _check_dataset_availability(self) -> List[str]:
        """检查数据集中可用的受试者"""
        available_subjects = []
        
        logger.info("检查完整数据集可用性...")
        
        for subject_id in self.test_subjects:
            subject_path = os.path.join(self.dataset_path, subject_id)
            if os.path.exists(subject_path):
                # 检查必需的文件
                required_files = [
                    f"{subject_id}R06.edf",
                    f"{subject_id}R06.edf.event", 
                    f"{subject_id}R10.edf",
                    f"{subject_id}R10.edf.event"
                ]
                
                files_exist = all(
                    os.path.exists(os.path.join(subject_path, file)) 
                    for file in required_files
                )
                
                if files_exist:
                    available_subjects.append(subject_id)
                    logger.info(f"✓ {subject_id}: 数据文件完整")
                else:
                    logger.warning(f"✗ {subject_id}: 缺少必需文件")
            else:
                logger.warning(f"✗ {subject_id}: 目录不存在")
        
        logger.info(f"可用受试者数量: {len(available_subjects)}/{len(self.test_subjects)}")
        return available_subjects
    
    def _generate_comprehensive_report(self, results_no_prep: Dict[str, Any], 
                                     results_with_prep: Dict[str, Any],
                                     tested_subjects: List[str]) -> Dict[str, Any]:
        """生成综合对比报告"""
        logger.info("生成完整的10名受试者对比报告...")
        
        # 算法名称映射
        algorithm_names = {
            'fbcsp_svm': 'FBCSP + SVM',
            'tef_rf': 'TEF + RandomForest',
            'riemannian_meanfield': 'Riemannian + MeanField',
            'tangent_space_lr': 'TangentSpace + LogisticRegression',
            'plv_svm': 'PLV + SVM'
        }
        
        comprehensive_report = {
            'validation_info': {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'dataset_path': self.dataset_path,
                'tested_subjects': tested_subjects,
                'total_subjects': len(tested_subjects),
                'validation_type': '完整10名受试者预处理前后对比',
                'preprocessing_details': {
                    'no_preprocessing': '直接使用原始PhysioNet数据',
                    'with_preprocessing': '8-30Hz带通滤波 + 50Hz陷波 + RLS自适应滤波 + Z-score标准化'
                }
            },
            'no_preprocessing_results': results_no_prep,
            'with_preprocessing_results': results_with_prep,
            'algorithm_comparison': {},
            'statistical_analysis': {},
            'preprocessing_impact_summary': {}
        }
        
        # 算法性能对比分析
        if ('summary_statistics' in results_no_prep and 'algorithms' in results_no_prep['summary_statistics'] and
            'summary_statistics' in results_with_prep and 'algorithms' in results_with_prep['summary_statistics']):
            
            no_prep_algorithms = results_no_prep['summary_statistics']['algorithms']
            with_prep_algorithms = results_with_prep['summary_statistics']['algorithms']
            
            for alg_key, alg_name in algorithm_names.items():
                if alg_key in no_prep_algorithms and alg_key in with_prep_algorithms:
                    no_prep_data = no_prep_algorithms[alg_key]
                    with_prep_data = with_prep_algorithms[alg_key]
                    
                    no_prep_acc = no_prep_data.get('mean_cv_accuracy', 0)
                    no_prep_std = no_prep_data.get('std_cv_accuracy', 0)
                    with_prep_acc = with_prep_data.get('mean_cv_accuracy', 0)
                    with_prep_std = with_prep_data.get('std_cv_accuracy', 0)
                    
                    improvement = with_prep_acc - no_prep_acc
                    improvement_percent = (improvement / no_prep_acc * 100) if no_prep_acc > 0 else 0
                    
                    comprehensive_report['algorithm_comparison'][alg_name] = {
                        'no_preprocessing': {
                            'accuracy': no_prep_acc,
                            'std': no_prep_std,
                            'subjects': no_prep_data.get('n_subjects', 0)
                        },
                        'with_preprocessing': {
                            'accuracy': with_prep_acc,
                            'std': with_prep_std,
                            'subjects': with_prep_data.get('n_subjects', 0)
                        },
                        'improvement': improvement,
                        'improvement_percent': improvement_percent,
                        'algorithm_key': alg_key
                    }
        
        # 统计显著性分析
        comprehensive_report['statistical_analysis'] = self._perform_statistical_analysis(
            comprehensive_report['algorithm_comparison']
        )
        
        # 总体影响摘要
        if comprehensive_report['algorithm_comparison']:
            improvements = [
                data['improvement'] for data in comprehensive_report['algorithm_comparison'].values()
                if 'improvement' in data
            ]
            
            if improvements:
                avg_improvement = np.mean(improvements)
                std_improvement = np.std(improvements)
                improved_count = sum(1 for imp in improvements if imp > 0)
                degraded_count = sum(1 for imp in improvements if imp < 0)
                
                comprehensive_report['preprocessing_impact_summary'] = {
                    'average_improvement': avg_improvement,
                    'std_improvement': std_improvement,
                    'improved_algorithms': improved_count,
                    'degraded_algorithms': degraded_count,
                    'total_algorithms': len(improvements),
                    'overall_impact': 'positive' if avg_improvement > 0 else 'negative',
                    'effect_size': 'large' if abs(avg_improvement) > 0.05 else 'medium' if abs(avg_improvement) > 0.02 else 'small'
                }
        
        return comprehensive_report
    
    def _perform_statistical_analysis(self, algorithm_comparison: Dict) -> Dict[str, Any]:
        """执行统计显著性分析"""
        statistical_results = {}
        
        for alg_name, data in algorithm_comparison.items():
            if 'improvement' in data:
                improvement = data['improvement']
                no_prep_std = data['no_preprocessing']['std']
                with_prep_std = data['with_preprocessing']['std']
                n_subjects = data['no_preprocessing']['subjects']
                
                # 计算效应量 (Cohen's d)
                pooled_std = np.sqrt((no_prep_std**2 + with_prep_std**2) / 2)
                cohens_d = improvement / pooled_std if pooled_std > 0 else 0
                
                # 计算置信区间 (简化版本)
                se = pooled_std / np.sqrt(n_subjects) if n_subjects > 0 else 0
                ci_lower = improvement - 1.96 * se
                ci_upper = improvement + 1.96 * se
                
                statistical_results[alg_name] = {
                    'cohens_d': cohens_d,
                    'effect_size': 'large' if abs(cohens_d) > 0.8 else 'medium' if abs(cohens_d) > 0.5 else 'small',
                    'confidence_interval_95': [ci_lower, ci_upper],
                    'improvement_significant': abs(cohens_d) > 0.2  # 小效应量阈值
                }
        
        return statistical_results
    
    def _save_comprehensive_results(self, comprehensive_report: Dict[str, Any]):
        """保存综合结果"""
        # 转换numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.bool_):
                return bool(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            else:
                return obj

        # 转换报告中的numpy类型
        converted_report = convert_numpy_types(comprehensive_report)

        # 保存详细报告
        report_file = "comprehensive_10_subjects_validation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(converted_report, f, indent=2, ensure_ascii=False)
        
        # 生成详细的文本报告
        self._save_detailed_text_report(comprehensive_report)
        
        logger.info(f"完整10名受试者验证报告已保存: {report_file}")
    
    def _save_detailed_text_report(self, comprehensive_report: Dict[str, Any]):
        """保存详细的文本报告"""
        try:
            report_lines = []
            
            # 报告头部
            report_lines.append("完整10名受试者预处理前后对比验证报告")
            report_lines.append("=" * 80)
            report_lines.append(f"生成时间: {comprehensive_report['validation_info']['timestamp']}")
            report_lines.append(f"测试受试者: {comprehensive_report['validation_info']['total_subjects']}名")
            report_lines.append(f"验证类型: {comprehensive_report['validation_info']['validation_type']}")
            report_lines.append("")
            
            # 预处理详情
            report_lines.append("预处理配置对比")
            report_lines.append("-" * 50)
            prep_details = comprehensive_report['validation_info']['preprocessing_details']
            report_lines.append(f"无预处理: {prep_details['no_preprocessing']}")
            report_lines.append(f"有预处理: {prep_details['with_preprocessing']}")
            report_lines.append("")
            
            # 详细性能对比表格
            if 'algorithm_comparison' in comprehensive_report:
                report_lines.append("详细性能对比表")
                report_lines.append("-" * 100)
                report_lines.append(f"{'算法名称':<30} {'无预处理':<15} {'有预处理':<15} {'改进幅度':<15} {'效应量':<15}")
                report_lines.append("-" * 100)
                
                for alg_name, data in comprehensive_report['algorithm_comparison'].items():
                    no_prep = f"{data['no_preprocessing']['accuracy']:.3f}±{data['no_preprocessing']['std']:.3f}"
                    with_prep = f"{data['with_preprocessing']['accuracy']:.3f}±{data['with_preprocessing']['std']:.3f}"
                    
                    improvement = data['improvement']
                    improvement_percent = data['improvement_percent']
                    
                    if improvement >= 0:
                        improvement_str = f"+{improvement:.3f} ({improvement_percent:+.1f}%)"
                    else:
                        improvement_str = f"{improvement:.3f} ({improvement_percent:+.1f}%)"
                    
                    # 获取效应量
                    effect_size = "N/A"
                    if 'statistical_analysis' in comprehensive_report and alg_name in comprehensive_report['statistical_analysis']:
                        effect_size = comprehensive_report['statistical_analysis'][alg_name]['effect_size']
                    
                    report_lines.append(f"{alg_name:<30} {no_prep:<15} {with_prep:<15} {improvement_str:<15} {effect_size:<15}")
                
                report_lines.append("-" * 100)
            
            # 总体影响摘要
            if 'preprocessing_impact_summary' in comprehensive_report:
                summary = comprehensive_report['preprocessing_impact_summary']
                report_lines.append("")
                report_lines.append("总体预处理影响摘要")
                report_lines.append("=" * 50)
                report_lines.append(f"平均性能提升: {summary.get('average_improvement', 0):.3f} ± {summary.get('std_improvement', 0):.3f}")
                report_lines.append(f"性能提升算法: {summary.get('improved_algorithms', 0)}/{summary.get('total_algorithms', 0)}")
                report_lines.append(f"性能下降算法: {summary.get('degraded_algorithms', 0)}/{summary.get('total_algorithms', 0)}")
                report_lines.append(f"总体影响: {summary.get('overall_impact', 'unknown')}")
                report_lines.append(f"效应量级别: {summary.get('effect_size', 'unknown')}")
            
            # 统计显著性分析
            if 'statistical_analysis' in comprehensive_report:
                report_lines.append("")
                report_lines.append("统计显著性分析")
                report_lines.append("=" * 50)
                
                for alg_name, stats in comprehensive_report['statistical_analysis'].items():
                    report_lines.append(f"\n📊 {alg_name}:")
                    report_lines.append(f"   Cohen's d: {stats.get('cohens_d', 0):.3f}")
                    report_lines.append(f"   效应量: {stats.get('effect_size', 'unknown')}")
                    
                    ci = stats.get('confidence_interval_95', [0, 0])
                    report_lines.append(f"   95%置信区间: [{ci[0]:.3f}, {ci[1]:.3f}]")
                    
                    significant = stats.get('improvement_significant', False)
                    report_lines.append(f"   显著性: {'✅ 显著' if significant else '❌ 不显著'}")
            
            # 结论和建议
            report_lines.append("")
            report_lines.append("结论和建议")
            report_lines.append("=" * 30)
            
            if 'preprocessing_impact_summary' in comprehensive_report:
                summary = comprehensive_report['preprocessing_impact_summary']
                overall_impact = summary.get('overall_impact', 'unknown')
                
                if overall_impact == 'positive':
                    report_lines.append("✅ 建议启用预处理管道")
                    report_lines.append("   理由：总体性能提升，统计结果支持")
                else:
                    report_lines.append("⚠️  需要进一步分析预处理配置")
                    report_lines.append("   理由：总体性能下降，需要调整参数")
            
            report_lines.append("")
            report_lines.append("🔬 技术建议:")
            report_lines.append("   1. 对于显著提升的算法，立即部署预处理管道")
            report_lines.append("   2. 对于性能下降的算法，调整预处理参数")
            report_lines.append("   3. 监控实际使用中的性能表现")
            report_lines.append("   4. 定期重新评估预处理效果")
            
            # 保存文本报告
            text_report_file = "comprehensive_10_subjects_validation_summary.txt"
            with open(text_report_file, 'w', encoding='utf-8') as f:
                f.write("\n".join(report_lines))
            
            logger.info(f"详细文本报告已保存: {text_report_file}")
            
        except Exception as e:
            logger.warning(f"保存详细文本报告失败: {e}")


def main():
    """主函数"""
    # 数据集路径
    dataset_path = r"D:\脑电\数据\EEG Motor MovementImagery Dataset"
    
    # 检查数据集路径
    if not os.path.exists(dataset_path):
        logger.error(f"数据集路径不存在: {dataset_path}")
        return
    
    try:
        # 创建验证器
        validator = Comprehensive10SubjectsValidator(dataset_path)
        
        # 执行完整验证
        logger.info("开始完整的10名受试者验证，预计需要30-60分钟...")
        results = validator.run_comprehensive_validation(cv_folds=5)
        
        # 打印简要结果
        print("\n" + "="*80)
        print("完整10名受试者预处理前后对比验证结果摘要")
        print("="*80)
        
        if 'preprocessing_impact_summary' in results:
            summary = results['preprocessing_impact_summary']
            print(f"\n总体影响: {summary.get('overall_impact', 'unknown')}")
            print(f"平均性能提升: {summary.get('average_improvement', 0):.3f} ± {summary.get('std_improvement', 0):.3f}")
            print(f"性能提升算法: {summary.get('improved_algorithms', 0)}/{summary.get('total_algorithms', 0)}")
            print(f"效应量级别: {summary.get('effect_size', 'unknown')}")
        
        if 'algorithm_comparison' in results:
            print("\n各算法预处理影响:")
            for alg_name, data in results['algorithm_comparison'].items():
                improvement = data.get('improvement', 0)
                improvement_percent = data.get('improvement_percent', 0)
                impact = "↑" if improvement > 0 else "↓" if improvement < 0 else "→"
                print(f"{impact} {alg_name}: {improvement_percent:+.1f}%")
        
        print(f"\n详细结果已保存到当前目录")
        print("="*80)
        
    except Exception as e:
        logger.error(f"完整验证过程失败: {e}")
        raise


if __name__ == "__main__":
    main()
